// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'speaking_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$speakingControllerHash() =>
    r'bb1df2afb7d4f12d87b3ee762e189dbce96159f3';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$SpeakingController
    extends BuildlessAutoDisposeAsyncNotifier<SpeakingState> {
  late final String level;
  late final String chapter;
  late final String path;
  late final SpeakingStage? stage;

  FutureOr<SpeakingState> build(
    String level,
    String chapter,
    String path,
    SpeakingStage? stage,
  );
}

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
///
/// Copied from [SpeakingController].
@ProviderFor(SpeakingController)
const speakingControllerProvider = SpeakingControllerFamily();

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
///
/// Copied from [SpeakingController].
class SpeakingControllerFamily extends Family<AsyncValue<SpeakingState>> {
  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  ///
  /// Copied from [SpeakingController].
  const SpeakingControllerFamily();

  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  ///
  /// Copied from [SpeakingController].
  SpeakingControllerProvider call(
    String level,
    String chapter,
    String path,
    SpeakingStage? stage,
  ) {
    return SpeakingControllerProvider(level, chapter, path, stage);
  }

  @override
  SpeakingControllerProvider getProviderOverride(
    covariant SpeakingControllerProvider provider,
  ) {
    return call(
      provider.level,
      provider.chapter,
      provider.path,
      provider.stage,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'speakingControllerProvider';
}

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
///
/// Copied from [SpeakingController].
class SpeakingControllerProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          SpeakingController,
          SpeakingState
        > {
  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  ///
  /// Copied from [SpeakingController].
  SpeakingControllerProvider(
    String level,
    String chapter,
    String path,
    SpeakingStage? stage,
  ) : this._internal(
        () =>
            SpeakingController()
              ..level = level
              ..chapter = chapter
              ..path = path
              ..stage = stage,
        from: speakingControllerProvider,
        name: r'speakingControllerProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$speakingControllerHash,
        dependencies: SpeakingControllerFamily._dependencies,
        allTransitiveDependencies:
            SpeakingControllerFamily._allTransitiveDependencies,
        level: level,
        chapter: chapter,
        path: path,
        stage: stage,
      );

  SpeakingControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.level,
    required this.chapter,
    required this.path,
    required this.stage,
  }) : super.internal();

  final String level;
  final String chapter;
  final String path;
  final SpeakingStage? stage;

  @override
  FutureOr<SpeakingState> runNotifierBuild(
    covariant SpeakingController notifier,
  ) {
    return notifier.build(level, chapter, path, stage);
  }

  @override
  Override overrideWith(SpeakingController Function() create) {
    return ProviderOverride(
      origin: this,
      override: SpeakingControllerProvider._internal(
        () =>
            create()
              ..level = level
              ..chapter = chapter
              ..path = path
              ..stage = stage,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        level: level,
        chapter: chapter,
        path: path,
        stage: stage,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<SpeakingController, SpeakingState>
  createElement() {
    return _SpeakingControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SpeakingControllerProvider &&
        other.level == level &&
        other.chapter == chapter &&
        other.path == path &&
        other.stage == stage;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, level.hashCode);
    hash = _SystemHash.combine(hash, chapter.hashCode);
    hash = _SystemHash.combine(hash, path.hashCode);
    hash = _SystemHash.combine(hash, stage.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SpeakingControllerRef
    on AutoDisposeAsyncNotifierProviderRef<SpeakingState> {
  /// The parameter `level` of this provider.
  String get level;

  /// The parameter `chapter` of this provider.
  String get chapter;

  /// The parameter `path` of this provider.
  String get path;

  /// The parameter `stage` of this provider.
  SpeakingStage? get stage;
}

class _SpeakingControllerProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          SpeakingController,
          SpeakingState
        >
    with SpeakingControllerRef {
  _SpeakingControllerProviderElement(super.provider);

  @override
  String get level => (origin as SpeakingControllerProvider).level;
  @override
  String get chapter => (origin as SpeakingControllerProvider).chapter;
  @override
  String get path => (origin as SpeakingControllerProvider).path;
  @override
  SpeakingStage? get stage => (origin as SpeakingControllerProvider).stage;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
