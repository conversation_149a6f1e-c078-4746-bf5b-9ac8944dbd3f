import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/firestore_service_service/domain/providers/firestore_service_provider.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import '../../../../mocks/firebase_mocks.dart';
import '../../../../helpers/riverpod_test_utils.dart';

// Additional mock classes for this test
class MockFirebaseFunctions extends Mock implements FirebaseFunctions {}

class MockHttpsCallable extends Mock implements HttpsCallable {}

void main() {
  group('firestoreServiceRepositoryProvider', () {
    late ProviderContainer container;
    late FakeFirebaseFirestore fakeFirestore;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockFirebaseStorage mockFirebaseStorage;
    late MockFirebaseFunctions mockFirebaseFunctions;

    setUp(() {
      // Create fake and mock services
      fakeFirestore = FakeFirebaseFirestore();
      mockFirebaseAuth = MockFirebaseAuth();
      mockFirebaseStorage = MockFirebaseStorage();
      mockFirebaseFunctions = MockFirebaseFunctions();

      // Setup Firebase Auth mocks
      final mockUser = MockUser();
      when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
      when(() => mockUser.uid).thenReturn('test_user_id');
      when(() => mockUser.email).thenReturn('<EMAIL>');

      // Setup Firebase Storage mocks
      final mockReference = MockReference();
      final mockReferenceWithPath = MockReference();
      when(() => mockFirebaseStorage.ref()).thenReturn(mockReference);
      when(
        () => mockFirebaseStorage.ref('test/path'),
      ).thenReturn(mockReferenceWithPath);
      when(() => mockReference.fullPath).thenReturn('');
      when(() => mockReference.name).thenReturn('');
      when(() => mockReferenceWithPath.fullPath).thenReturn('test/path');
      when(() => mockReferenceWithPath.name).thenReturn('path');

      // Setup Firebase Functions mocks
      final mockHttpsCallable = MockHttpsCallable();
      when(
        () => mockFirebaseFunctions.httpsCallable(any()),
      ).thenReturn(mockHttpsCallable);

      // Register fallback values for mocktail
      registerFallbackValue(<String, dynamic>{});
      registerFallbackValue(Uri.parse('https://example.com'));
      registerFallbackValue(const Duration(seconds: 1));
      registerFallbackValue('asia-southeast1');

      // Create container with provider override using test services
      container = ProviderContainer(
        overrides: [
          firestoreServiceRepositoryProvider.overrideWith(
            (ref) => FirestoreServiceRepositoryImpl(
              fakeFirestore,
              mockFirebaseAuth,
              mockFirebaseStorage,
              mockFirebaseFunctions,
            ),
          ),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('Provider Creation', () {
      test('should create FirestoreServiceRepositoryImpl instance', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository, isA<FirestoreServiceRepositoryImpl>());
      });

      test(
        'should return the same instance on multiple reads (singleton behavior)',
        () {
          // Act
          final repository1 = container.read(
            firestoreServiceRepositoryProvider,
          );
          final repository2 = container.read(
            firestoreServiceRepositoryProvider,
          );

          // Assert
          expect(identical(repository1, repository2), isTrue);
        },
      );

      test('should create repository with correct Firebase services', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.fireStore, equals(fakeFirestore));
        expect(repository.firebaseAuth, equals(mockFirebaseAuth));
        expect(repository.firebaseStorage, equals(mockFirebaseStorage));
        expect(repository.firebaseFunctions, equals(mockFirebaseFunctions));
      });
    });

    group('Firebase Service Initialization', () {
      test('should initialize Firebase Auth service', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseAuth, isNotNull);
        expect(repository.firebaseAuth, isA<FirebaseAuth>());
      });

      test('should initialize Firebase Firestore service', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.fireStore, isNotNull);
        expect(repository.fireStore, isA<FirebaseFirestore>());
      });

      test('should initialize Firebase Storage service', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseStorage, isNotNull);
        expect(repository.firebaseStorage, isA<FirebaseStorage>());
      });

      test('should initialize Firebase Functions service', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseFunctions, isNotNull);
        expect(repository.firebaseFunctions, isA<FirebaseFunctions>());
      });

      test('should provide access to Firebase Auth current user', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseAuth.currentUser, isNotNull);
        expect(
          repository.firebaseAuth.currentUser?.uid,
          equals('test_user_id'),
        );
      });

      test('should allow Firestore collection access through getter', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);
        final collection = repository.fireStore.collection('test-collection');

        // Assert
        expect(collection, isNotNull);
        expect(collection.path, equals('test-collection'));
      });

      test('should allow Firebase Storage bucket access through getter', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);
        final storageRef = repository.firebaseStorage.ref();

        // Assert
        expect(storageRef, isNotNull);
        expect(storageRef.fullPath, equals(''));
      });

      test(
        'should allow Firebase Functions callable access through getter',
        () {
          // Act
          final repository = container.read(firestoreServiceRepositoryProvider);
          final callable = repository.firebaseFunctions.httpsCallable(
            'testFunction',
          );

          // Assert
          expect(callable, isNotNull);
        },
      );
    });

    group('Firebase Functions Region Configuration', () {
      test('should configure Firebase Functions with asia-southeast1 region', () {
        // This test verifies that the provider correctly configures the region
        // We can't directly test the region configuration without mocking FirebaseFunctions.instanceFor
        // but we can verify that the provider creates a repository with the expected FirebaseFunctions instance

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseFunctions, isNotNull);
        // The actual region configuration is tested indirectly through the repository's behavior
      });

      test('should handle Firebase Functions initialization correctly', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseFunctions, equals(mockFirebaseFunctions));
      });

      test(
        'should use Firebase Functions instance with correct region in provider',
        () {
          // This test verifies that the provider uses FirebaseFunctions.instanceFor with the correct region
          // Since we can't mock the static method, we verify the instance is created correctly

          // Act
          final repository = container.read(firestoreServiceRepositoryProvider);

          // Assert
          expect(repository.firebaseFunctions, isA<FirebaseFunctions>());
          // In a real test environment, you might verify the region through behavior
        },
      );

      test('should allow Firebase Functions calls with configured region', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);
        final callable = repository.firebaseFunctions.httpsCallable(
          'testCallable',
        );

        // Assert
        expect(callable, isNotNull);
        // The callable should work with the configured region
      });
    });

    group('Repository Interface Implementation', () {
      test('should implement FirestoreServiceRepository interface', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository, isA<FirestoreServiceRepository>());
      });

      test('should provide access to dataUser method', () {
        // Arrange
        final mockUser = MockUser();
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.uid).thenReturn('test_user_id');

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);
        final documentRef = repository.dataUser();

        // Assert
        expect(documentRef, isNotNull);
        expect(documentRef.path, equals('user-data/test_user_id'));
      });

      test('should handle unauthenticated user in dataUser method', () {
        // Arrange
        when(() => mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);
        final documentRef = repository.dataUser();

        // Assert
        expect(documentRef, isNotNull);
        expect(documentRef.parent.path, equals('user-data'));
        expect(documentRef.id.isNotEmpty, isTrue);
      });

      test('should handle user with empty uid in dataUser method', () {
        // Arrange
        final mockUser = MockUser();
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.uid).thenReturn('');

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);
        final documentRef = repository.dataUser();

        // Assert
        expect(documentRef, isNotNull);
        expect(documentRef.path, equals('user-data/'));
      });

      test('should return consistent document reference for same user', () {
        // Arrange
        final mockUser = MockUser();
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.uid).thenReturn('consistent_user_id');

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);
        final docRef1 = repository.dataUser();
        final docRef2 = repository.dataUser();

        // Assert
        expect(docRef1.path, equals(docRef2.path));
        expect(
          identical(docRef1, docRef2),
          isFalse,
        ); // Different instances but same path
      });
    });

    group('Provider Dependencies', () {
      test('should not depend on external providers', () {
        // This test verifies that the provider only depends on Firebase services
        // and doesn't require other application-specific providers

        // Act & Assert - No additional setup needed
        expect(
          () => container.read(firestoreServiceRepositoryProvider),
          returnsNormally,
        );
      });

      test('should work with minimal Firebase service setup', () {
        // This test ensures the provider works with just the basic Firebase services

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository, isNotNull);
        expect(repository.fireStore, isNotNull);
        expect(repository.firebaseAuth, isNotNull);
        expect(repository.firebaseStorage, isNotNull);
        expect(repository.firebaseFunctions, isNotNull);
      });
    });

    group('Provider Configuration Variations', () {
      test('should work with custom Firebase Firestore instance', () {
        // Arrange
        final customFirestore = FakeFirebaseFirestore();

        // Act
        final testContainer = ProviderContainer(
          overrides: [
            firestoreServiceRepositoryProvider.overrideWith(
              (ref) => FirestoreServiceRepositoryImpl(
                customFirestore,
                mockFirebaseAuth,
                mockFirebaseStorage,
                mockFirebaseFunctions,
              ),
            ),
          ],
        );

        // Assert
        final repository = testContainer.read(
          firestoreServiceRepositoryProvider,
        );
        expect(repository.fireStore, equals(customFirestore));

        testContainer.dispose();
      });

      test('should work with custom Firebase Auth instance', () {
        // Arrange
        final customAuth = MockFirebaseAuth();
        final mockUser = MockUser();
        when(() => customAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.uid).thenReturn('custom_auth_user');

        // Act
        final testContainer = ProviderContainer(
          overrides: [
            firestoreServiceRepositoryProvider.overrideWith(
              (ref) => FirestoreServiceRepositoryImpl(
                fakeFirestore,
                customAuth,
                mockFirebaseStorage,
                mockFirebaseFunctions,
              ),
            ),
          ],
        );

        // Assert
        final repository = testContainer.read(
          firestoreServiceRepositoryProvider,
        );
        expect(repository.firebaseAuth, equals(customAuth));
        expect(
          repository.firebaseAuth.currentUser?.uid,
          equals('custom_auth_user'),
        );

        testContainer.dispose();
      });

      test('should work with custom Firebase Storage instance', () {
        // Arrange
        final customStorage = MockFirebaseStorage();

        // Act
        final testContainer = ProviderContainer(
          overrides: [
            firestoreServiceRepositoryProvider.overrideWith(
              (ref) => FirestoreServiceRepositoryImpl(
                fakeFirestore,
                mockFirebaseAuth,
                customStorage,
                mockFirebaseFunctions,
              ),
            ),
          ],
        );

        // Assert
        final repository = testContainer.read(
          firestoreServiceRepositoryProvider,
        );
        expect(repository.firebaseStorage, equals(customStorage));

        testContainer.dispose();
      });

      test('should work with custom Firebase Functions instance', () {
        // Arrange
        final customFunctions = MockFirebaseFunctions();

        // Act
        final testContainer = ProviderContainer(
          overrides: [
            firestoreServiceRepositoryProvider.overrideWith(
              (ref) => FirestoreServiceRepositoryImpl(
                fakeFirestore,
                mockFirebaseAuth,
                mockFirebaseStorage,
                customFunctions,
              ),
            ),
          ],
        );

        // Assert
        final repository = testContainer.read(
          firestoreServiceRepositoryProvider,
        );
        expect(repository.firebaseFunctions, equals(customFunctions));

        testContainer.dispose();
      });
    });

    group('Error Handling', () {
      test('should handle Firebase service initialization errors gracefully', () {
        // This test would require mocking Firebase service failures
        // For now, we verify that the provider doesn't throw during normal operation

        // Act & Assert
        expect(
          () => container.read(firestoreServiceRepositoryProvider),
          returnsNormally,
        );
      });

      test('should create repository even when Firebase services have issues', () {
        // Arrange - Setup a scenario where Firebase services might have issues
        // (This is a simplified test - in real scenarios, you might mock specific failures)

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository, isNotNull);
        // The repository should still be created even if individual services have issues
      });

      test('should handle Firebase Auth exceptions during user access', () {
        // Arrange
        when(
          () => mockFirebaseAuth.currentUser,
        ).thenThrow(FirebaseAuthException(code: 'user-not-found'));

        // Act & Assert
        expect(
          () => container.read(firestoreServiceRepositoryProvider).dataUser(),
          throwsA(isA<FirebaseAuthException>()),
        );
      });

      test(
        'should handle Firebase Firestore exceptions during document access',
        () {
          // Arrange
          final mockUser = MockUser();
          when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
          when(() => mockUser.uid).thenReturn('test_user_id');
          // Note: In real scenarios, you might need to mock Firestore exceptions

          // Act
          final repository = container.read(firestoreServiceRepositoryProvider);

          // Assert - Repository should still be created
          expect(repository, isNotNull);
          expect(repository.fireStore, isNotNull);
        },
      );

      test('should gracefully handle null Firebase services in repository', () {
        // This test verifies behavior when services might be null
        // In practice, Firebase services are rarely null, but good to test defensive programming

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.fireStore, isNotNull);
        expect(repository.firebaseAuth, isNotNull);
        expect(repository.firebaseStorage, isNotNull);
        expect(repository.firebaseFunctions, isNotNull);
      });

      test('should handle provider read failures', () {
        // Arrange - Dispose container to simulate failure
        container.dispose();

        // Act & Assert
        expect(
          () => container.read(firestoreServiceRepositoryProvider),
          throwsStateError,
        );
      });
    });

    group('Provider Lifecycle', () {
      test('should properly dispose resources when container is disposed', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);
        container.dispose();

        // Assert - The repository should still exist but the container should be disposed
        expect(repository, isNotNull);
        expect(
          () => container.read(firestoreServiceRepositoryProvider),
          throwsStateError,
        );
      });

      test('should maintain repository instance across container refresh', () {
        // Act
        final repository1 = container.read(firestoreServiceRepositoryProvider);
        container.refresh(firestoreServiceRepositoryProvider);
        final repository2 = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository1, isNotNull);
        expect(repository2, isNotNull);
        // Note: Riverpod providers create new instances on refresh by default
        // This test documents the expected behavior
      });
    });

    group('Performance and Resource Management', () {
      test('should create repository instance efficiently', () {
        // Act
        final startTime = DateTime.now();
        final repository = container.read(firestoreServiceRepositoryProvider);
        final endTime = DateTime.now();

        // Assert
        expect(repository, isNotNull);
        // Repository creation should be fast (less than 100ms in typical scenarios)
        expect(endTime.difference(startTime).inMilliseconds, lessThan(1000));
      });

      test('should handle multiple rapid provider reads efficiently', () {
        // Act
        final startTime = DateTime.now();
        for (int i = 0; i < 100; i++) {
          final repository = container.read(firestoreServiceRepositoryProvider);
          expect(repository, isNotNull);
        }
        final endTime = DateTime.now();

        // Assert
        // Multiple reads should be efficient
        expect(endTime.difference(startTime).inMilliseconds, lessThan(500));
      });

      test(
        'should maintain consistent performance across multiple containers',
        () {
          // Act
          final containers = <ProviderContainer>[];
          final repositories = <FirestoreServiceRepository>[];

          for (int i = 0; i < 10; i++) {
            final testContainer = ProviderContainer(
              overrides: [
                firestoreServiceRepositoryProvider.overrideWith(
                  (ref) => FirestoreServiceRepositoryImpl(
                    fakeFirestore,
                    mockFirebaseAuth,
                    mockFirebaseStorage,
                    mockFirebaseFunctions,
                  ),
                ),
              ],
            );
            containers.add(testContainer);
            repositories.add(
              testContainer.read(firestoreServiceRepositoryProvider),
            );
          }

          // Assert
          expect(repositories.length, equals(10));
          for (var repo in repositories) {
            expect(repo, isNotNull);
          }

          // Cleanup
          for (var c in containers) {
            c.dispose();
          }
        },
      );

      test('should not leak resources when creating multiple instances', () {
        // This test ensures that creating multiple repository instances doesn't cause issues

        // Act
        final repositories = <FirestoreServiceRepository>[];
        for (int i = 0; i < 50; i++) {
          final testContainer = ProviderContainer(
            overrides: [
              firestoreServiceRepositoryProvider.overrideWith(
                (ref) => FirestoreServiceRepositoryImpl(
                  fakeFirestore,
                  mockFirebaseAuth,
                  mockFirebaseStorage,
                  mockFirebaseFunctions,
                ),
              ),
            ],
          );
          repositories.add(
            testContainer.read(firestoreServiceRepositoryProvider),
          );
          testContainer.dispose();
        }

        // Assert
        expect(repositories.length, equals(50));
        for (var repo in repositories) {
          expect(repo, isNotNull);
        }
      });
    });

    group('Integration with Riverpod Test Utils', () {
      test('should work with RiverpodTestUtils.createTestContainer', () {
        // Act
        final testContainer = RiverpodTestUtils.createTestContainer(
          additionalOverrides: [
            firestoreServiceRepositoryProvider.overrideWith(
              (ref) => FirestoreServiceRepositoryImpl(
                fakeFirestore,
                mockFirebaseAuth,
                mockFirebaseStorage,
                mockFirebaseFunctions,
              ),
            ),
          ],
        );

        // Assert
        expect(
          () => testContainer.read(firestoreServiceRepositoryProvider),
          returnsNormally,
        );

        testContainer.dispose();
      });

      test('should support provider override in tests', () {
        // Act
        final testContainer = ProviderContainer(
          overrides: [
            // Override the provider itself for specific test scenarios
            firestoreServiceRepositoryProvider.overrideWith(
              (ref) => FirestoreServiceRepositoryImpl(
                fakeFirestore,
                mockFirebaseAuth,
                mockFirebaseStorage,
                mockFirebaseFunctions,
              ),
            ),
          ],
        );

        // Assert
        final repository = testContainer.read(
          firestoreServiceRepositoryProvider,
        );
        expect(repository, isA<FirestoreServiceRepositoryImpl>());

        testContainer.dispose();
      });
    });

    group('Mock Integration', () {
      test('should work with direct mock instantiation', () {
        // This test verifies that mocks work correctly when created directly

        // Act
        final authMock = MockFirebaseAuth();
        final firestoreFake = FakeFirebaseFirestore();
        final storageMock = MockFirebaseStorage();
        final functionsMock = MockFirebaseFunctions();

        final testContainer = ProviderContainer(
          overrides: [
            firestoreServiceRepositoryProvider.overrideWith(
              (ref) => FirestoreServiceRepositoryImpl(
                firestoreFake,
                authMock,
                storageMock,
                functionsMock,
              ),
            ),
          ],
        );

        // Assert
        final repository = testContainer.read(
          firestoreServiceRepositoryProvider,
        );
        expect(repository.firebaseAuth, equals(authMock));
        expect(repository.fireStore, equals(firestoreFake));
        expect(repository.firebaseStorage, equals(storageMock));

        testContainer.dispose();
      });

      test('should support direct mock setup for complex test scenarios', () {
        // Arrange
        final mockUser = MockUser();
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.uid).thenReturn('test_user_id');
        when(() => mockUser.email).thenReturn('<EMAIL>');

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseAuth.currentUser, equals(mockUser));
      });

      test('should handle mock user authentication state changes', () {
        // Arrange
        final mockUser = MockUser();
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.uid).thenReturn('initial_user_id');

        // Act
        final repository1 = container.read(firestoreServiceRepositoryProvider);
        final docRef1 = repository1.dataUser();

        // Change user state
        when(() => mockUser.uid).thenReturn('changed_user_id');
        final repository2 = container.read(firestoreServiceRepositoryProvider);
        final docRef2 = repository2.dataUser();

        // Assert
        expect(docRef1.path, equals('user-data/initial_user_id'));
        expect(docRef2.path, equals('user-data/changed_user_id'));
      });

      test('should work with mock Firebase service interactions', () {
        // Arrange
        final mockUser = MockUser();
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.uid).thenReturn('interaction_test_user');

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Simulate service interactions
        final userDoc = repository.dataUser();
        final collection = repository.fireStore.collection('test');
        final storageRef = repository.firebaseStorage.ref('test/path');
        final callable = repository.firebaseFunctions.httpsCallable(
          'testFunction',
        );

        // Assert
        expect(userDoc.path, contains('interaction_test_user'));
        expect(collection.path, equals('test'));
        expect(storageRef.fullPath, equals('test/path'));
        expect(callable, isNotNull);
      });

      test('should support mock verification for method calls', () {
        // Arrange
        final mockUser = MockUser();
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.uid).thenReturn('verification_user');

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);
        final _ =
            repository.dataUser(); // Access to trigger potential verifications

        // Assert - In a real scenario, you might verify specific method calls
        // For now, we ensure the repository works with mocks
        expect(
          repository.firebaseAuth.currentUser?.uid,
          equals('verification_user'),
        );
      });
    });
  });
}
