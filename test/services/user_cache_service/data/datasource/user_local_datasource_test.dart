import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user/user_model.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/services/user_cache_service/data/datasource/user_local_datasource.dart';
import '../../../../helpers/mock_factories.dart';
import '../../../../helpers/test_data_builders.dart';
import '../../../../mocks/service_mocks.dart';

void main() {
  group('UserLocalDatasource Tests', () {
    late MockStorageService mockStorageService;
    late UserLocalDatasource userLocalDatasource;
    late User testUser;
    late String testUserJson;

    setUp(() {
      // Create mocks using centralized factories
      mockStorageService = MockFactories.createMockStorageService();

      // Setup common mock behaviors
      MockFactories.setupStorageServiceMocks(mockStorageService);

      // Create datasource with mocked dependency
      userLocalDatasource = UserLocalDatasource(mockStorageService);

      // Create test data using builders
      testUser =
          TestDataBuilders.userBuilder()
              .withId(123)
              .withEmail('<EMAIL>')
              .withFirstName('John')
              .withLastName('Doe')
              .withToken('auth_token_123')
              .build();

      testUserJson = jsonEncode(testUser.toJson());
    });

    tearDown(() {
      // Reset mocks between tests
      reset(mockStorageService);
    });

    group('storageKey getter', () {
      test('should return correct storage key', () {
        // Act
        final storageKey = userLocalDatasource.storageKey;

        // Assert
        expect(storageKey, equals(USER_LOCAL_STORAGE_KEY));
        expect(storageKey, equals('user'));
      });
    });

    group('fetchUser method', () {
      test('should return user successfully when data exists', () async {
        // Arrange
        when(
          () => mockStorageService.get(USER_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => testUserJson);

        // Act
        final result = await userLocalDatasource.fetchUser();

        // Assert
        expect(result.isRight(), isTrue);
        final user = result.fold((error) => null, (user) => user);
        expect(user, equals(testUser));
        verify(() => mockStorageService.get(USER_LOCAL_STORAGE_KEY)).called(1);
      });

      test('should return AppException when user data is null', () async {
        // Arrange
        when(
          () => mockStorageService.get(USER_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => null);

        // Act
        final result = await userLocalDatasource.fetchUser();

        // Assert
        expect(result.isLeft(), isTrue);
        final exception = result.fold((error) => error, (_) => null);
        expect(exception, isA<AppException>());
        expect(exception?.statusCode, equals(404));
        expect(exception?.message, equals('User not found'));
        expect(exception?.identifier, equals('UserLocalDatasource'));
        verify(() => mockStorageService.get(USER_LOCAL_STORAGE_KEY)).called(1);
      });

      test('should handle malformed JSON data', () async {
        // Arrange
        when(
          () => mockStorageService.get(USER_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => '{invalid json}');

        // Act & Assert
        expect(
          () => userLocalDatasource.fetchUser(),
          throwsA(isA<FormatException>()),
        );
        verify(() => mockStorageService.get(USER_LOCAL_STORAGE_KEY)).called(1);
      });

      test('should handle empty JSON string', () async {
        // Arrange
        when(
          () => mockStorageService.get(USER_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => '');

        // Act & Assert
        expect(
          () => userLocalDatasource.fetchUser(),
          throwsA(isA<FormatException>()),
        );
        verify(() => mockStorageService.get(USER_LOCAL_STORAGE_KEY)).called(1);
      });

      test('should handle storage service throwing exception', () async {
        // Arrange
        when(
          () => mockStorageService.get(USER_LOCAL_STORAGE_KEY),
        ).thenThrow(Exception('Storage error'));

        // Act & Assert
        expect(
          () => userLocalDatasource.fetchUser(),
          throwsA(isA<Exception>()),
        );
        verify(() => mockStorageService.get(USER_LOCAL_STORAGE_KEY)).called(1);
      });
    });

    group('saveUser method', () {
      test('should save user successfully', () async {
        // Arrange
        when(
          () => mockStorageService.set(USER_LOCAL_STORAGE_KEY, testUserJson),
        ).thenAnswer((_) async => true);

        // Act
        final result = await userLocalDatasource.saveUser(user: testUser);

        // Assert
        expect(result, isTrue);
        verify(
          () => mockStorageService.set(USER_LOCAL_STORAGE_KEY, testUserJson),
        ).called(1);
      });

      test('should handle storage service returning false', () async {
        // Arrange
        when(
          () => mockStorageService.set(USER_LOCAL_STORAGE_KEY, testUserJson),
        ).thenAnswer((_) async => false);

        // Act
        final result = await userLocalDatasource.saveUser(user: testUser);

        // Assert
        expect(result, isFalse);
        verify(
          () => mockStorageService.set(USER_LOCAL_STORAGE_KEY, testUserJson),
        ).called(1);
      });

      test('should handle storage service throwing exception', () async {
        // Arrange
        when(
          () => mockStorageService.set(USER_LOCAL_STORAGE_KEY, testUserJson),
        ).thenThrow(Exception('Storage error'));

        // Act & Assert
        expect(
          () => userLocalDatasource.saveUser(user: testUser),
          throwsA(isA<Exception>()),
        );
        verify(
          () => mockStorageService.set(USER_LOCAL_STORAGE_KEY, testUserJson),
        ).called(1);
      });

      test('should save user with empty fields', () async {
        // Arrange
        final emptyUser =
            TestDataBuilders.userBuilder().withDefaultValues().build();
        final emptyUserJson = jsonEncode(emptyUser.toJson());

        when(
          () => mockStorageService.set(USER_LOCAL_STORAGE_KEY, emptyUserJson),
        ).thenAnswer((_) async => true);

        // Act
        final result = await userLocalDatasource.saveUser(user: emptyUser);

        // Assert
        expect(result, isTrue);
        verify(
          () => mockStorageService.set(USER_LOCAL_STORAGE_KEY, emptyUserJson),
        ).called(1);
      });

      test('should save user with complete profile', () async {
        // Arrange
        final completeUser =
            TestDataBuilders.userBuilder().withCompleteProfile().build();
        final completeUserJson = jsonEncode(completeUser.toJson());

        when(
          () =>
              mockStorageService.set(USER_LOCAL_STORAGE_KEY, completeUserJson),
        ).thenAnswer((_) async => true);

        // Act
        final result = await userLocalDatasource.saveUser(user: completeUser);

        // Assert
        expect(result, isTrue);
        verify(
          () =>
              mockStorageService.set(USER_LOCAL_STORAGE_KEY, completeUserJson),
        ).called(1);
      });
    });

    group('deleteUser method', () {
      test('should delete user successfully', () async {
        // Arrange
        when(
          () => mockStorageService.remove(USER_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => true);

        // Act
        final result = await userLocalDatasource.deleteUser();

        // Assert
        expect(result, isTrue);
        verify(
          () => mockStorageService.remove(USER_LOCAL_STORAGE_KEY),
        ).called(1);
      });

      test('should handle storage service returning false', () async {
        // Arrange
        when(
          () => mockStorageService.remove(USER_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => false);

        // Act
        final result = await userLocalDatasource.deleteUser();

        // Assert
        expect(result, isFalse);
        verify(
          () => mockStorageService.remove(USER_LOCAL_STORAGE_KEY),
        ).called(1);
      });

      test('should handle storage service throwing exception', () async {
        // Arrange
        when(
          () => mockStorageService.remove(USER_LOCAL_STORAGE_KEY),
        ).thenThrow(Exception('Storage error'));

        // Act & Assert
        expect(
          () => userLocalDatasource.deleteUser(),
          throwsA(isA<Exception>()),
        );
        verify(
          () => mockStorageService.remove(USER_LOCAL_STORAGE_KEY),
        ).called(1);
      });
    });

    group('hasUser method', () {
      test('should return true when user exists', () async {
        // Arrange
        when(
          () => mockStorageService.has(USER_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => true);

        // Act
        final result = await userLocalDatasource.hasUser();

        // Assert
        expect(result, isTrue);
        verify(() => mockStorageService.has(USER_LOCAL_STORAGE_KEY)).called(1);
      });

      test('should return false when user does not exist', () async {
        // Arrange
        when(
          () => mockStorageService.has(USER_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => false);

        // Act
        final result = await userLocalDatasource.hasUser();

        // Assert
        expect(result, isFalse);
        verify(() => mockStorageService.has(USER_LOCAL_STORAGE_KEY)).called(1);
      });

      test('should handle storage service throwing exception', () async {
        // Arrange
        when(
          () => mockStorageService.has(USER_LOCAL_STORAGE_KEY),
        ).thenThrow(Exception('Storage error'));

        // Act & Assert
        expect(() => userLocalDatasource.hasUser(), throwsA(isA<Exception>()));
        verify(() => mockStorageService.has(USER_LOCAL_STORAGE_KEY)).called(1);
      });
    });

    group('Integration scenarios', () {
      test('should handle save and fetch user workflow', () async {
        // Arrange - Setup save operation
        when(
          () => mockStorageService.set(USER_LOCAL_STORAGE_KEY, testUserJson),
        ).thenAnswer((_) async => true);

        // Act - Save user
        final saveResult = await userLocalDatasource.saveUser(user: testUser);

        // Assert - Save successful
        expect(saveResult, isTrue);

        // Arrange - Setup fetch operation
        when(
          () => mockStorageService.get(USER_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => testUserJson);

        // Act - Fetch user
        final fetchResult = await userLocalDatasource.fetchUser();

        // Assert - Fetch successful and returns same user
        expect(fetchResult.isRight(), isTrue);
        final fetchedUser = fetchResult.fold((error) => null, (user) => user);
        expect(fetchedUser, equals(testUser));

        // Verify both operations called storage service
        verify(
          () => mockStorageService.set(USER_LOCAL_STORAGE_KEY, testUserJson),
        ).called(1);
        verify(() => mockStorageService.get(USER_LOCAL_STORAGE_KEY)).called(1);
      });

      test(
        'should handle save, check existence, and delete workflow',
        () async {
          // Arrange - Setup save operation
          when(
            () => mockStorageService.set(USER_LOCAL_STORAGE_KEY, testUserJson),
          ).thenAnswer((_) async => true);

          // Act - Save user
          final saveResult = await userLocalDatasource.saveUser(user: testUser);

          // Assert - Save successful
          expect(saveResult, isTrue);

          // Arrange - Setup has operation (user exists)
          when(
            () => mockStorageService.has(USER_LOCAL_STORAGE_KEY),
          ).thenAnswer((_) async => true);

          // Act - Check if user exists
          final hasUserResult = await userLocalDatasource.hasUser();

          // Assert - User exists
          expect(hasUserResult, isTrue);

          // Arrange - Setup delete operation
          when(
            () => mockStorageService.remove(USER_LOCAL_STORAGE_KEY),
          ).thenAnswer((_) async => true);

          // Act - Delete user
          final deleteResult = await userLocalDatasource.deleteUser();

          // Assert - Delete successful
          expect(deleteResult, isTrue);

          // Arrange - Setup has operation after delete (user doesn't exist)
          when(
            () => mockStorageService.has(USER_LOCAL_STORAGE_KEY),
          ).thenAnswer((_) async => false);

          // Act - Check if user exists after delete
          final hasUserAfterDelete = await userLocalDatasource.hasUser();

          // Assert - User no longer exists
          expect(hasUserAfterDelete, isFalse);

          // Verify all operations
          verify(
            () => mockStorageService.set(USER_LOCAL_STORAGE_KEY, testUserJson),
          ).called(1);
          verify(
            () => mockStorageService.has(USER_LOCAL_STORAGE_KEY),
          ).called(2);
          verify(
            () => mockStorageService.remove(USER_LOCAL_STORAGE_KEY),
          ).called(1);
        },
      );
    });

    group('Error handling and edge cases', () {
      test('should handle null user parameter in saveUser', () async {
        // Act & Assert
        expect(
          () => userLocalDatasource.saveUser(user: User()),
          returnsNormally,
        );
      });

      test('should handle concurrent operations', () async {
        // Arrange
        when(() => mockStorageService.get(USER_LOCAL_STORAGE_KEY)).thenAnswer((
          _,
        ) async {
          await Future.delayed(const Duration(milliseconds: 10));
          return testUserJson;
        });

        // Act - Start multiple concurrent fetch operations
        final futures = [
          userLocalDatasource.fetchUser(),
          userLocalDatasource.fetchUser(),
          userLocalDatasource.fetchUser(),
        ];

        // Assert - All operations complete successfully
        final results = await Future.wait(futures);
        expect(results.length, equals(3));
        for (final result in results) {
          expect(result.isRight(), isTrue);
          final user = result.fold((error) => null, (user) => user);
          expect(user, equals(testUser));
        }

        // Verify storage service called multiple times
        verify(() => mockStorageService.get(USER_LOCAL_STORAGE_KEY)).called(3);
      });

      test('should handle storage service timeout', () async {
        // Arrange
        when(() => mockStorageService.get(USER_LOCAL_STORAGE_KEY)).thenAnswer((
          _,
        ) async {
          await Future.delayed(const Duration(seconds: 10));
          return testUserJson;
        });

        // Act - Fetch with timeout
        final result = await userLocalDatasource.fetchUser().timeout(
          const Duration(milliseconds: 100),
          onTimeout:
              () => Left(
                AppException(
                  identifier: 'Timeout',
                  statusCode: 408,
                  message: 'Request timeout',
                ),
              ),
        );

        // Assert - Should timeout and return Left
        expect(result.isLeft(), isTrue);
        final exception = result.fold((error) => error, (_) => null);
        expect(exception?.statusCode, equals(408));
        expect(exception?.message, equals('Request timeout'));
      });
    });
  });
}
