import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/user_cache_service/domain/providers/user_cache_provider.dart';
import 'package:selfeng/services/user_cache_service/data/datasource/user_local_datasource.dart';
import 'package:selfeng/services/user_cache_service/data/repositories/user_repository_impl.dart';
import 'package:selfeng/services/user_cache_service/domain/repositories/user_cache_repository.dart';
import 'package:selfeng/shared/data/local/storage_service.dart';
import 'package:selfeng/shared/domain/providers/shared_preferences_storage_service_provider.dart';
import '../../../../mocks/service_mocks.dart';
import '../../../../mocks/repository_mocks.dart';
import '../../../../helpers/mock_factories.dart';
import '../../../../helpers/riverpod_test_utils.dart';

void main() {
  group('UserCacheProvider Tests', () {
    late ProviderContainer container;
    late MockSharedPrefsService mockSharedPrefsService;
    late MockStorageService mockStorageService;

    setUp(() {
      // Create mocks using centralized factories
      mockSharedPrefsService = MockFactories.createMockWithFallbacks(
        MockSharedPrefsService(),
      );
      mockStorageService = MockFactories.createMockWithFallbacks(
        MockStorageService(),
      );

      // Setup mock behaviors for SharedPrefsService
      when(() => mockSharedPrefsService.init()).thenAnswer((_) {});
      when(() => mockSharedPrefsService.hasInitialized).thenReturn(true);
      when(
        () => mockSharedPrefsService.get(any()),
      ).thenAnswer((_) async => null);
      when(
        () => mockSharedPrefsService.set(any(), any()),
      ).thenAnswer((_) async => true);
      when(
        () => mockSharedPrefsService.remove(any()),
      ).thenAnswer((_) async => true);
      when(
        () => mockSharedPrefsService.has(any()),
      ).thenAnswer((_) async => false);
      when(() => mockSharedPrefsService.clear()).thenAnswer((_) async {});

      // Setup mock behaviors for StorageService
      when(() => mockStorageService.init()).thenAnswer((_) {});
      when(() => mockStorageService.hasInitialized).thenReturn(true);
      when(() => mockStorageService.get(any())).thenAnswer((_) async => null);
      when(
        () => mockStorageService.set(any(), any()),
      ).thenAnswer((_) async => true);
      when(
        () => mockStorageService.remove(any()),
      ).thenAnswer((_) async => true);
      when(() => mockStorageService.has(any())).thenAnswer((_) async => false);
      when(() => mockStorageService.clear()).thenAnswer((_) async {});

      // Create container with overrides
      container = ProviderContainer(
        overrides: [
          storageServiceProvider.overrideWithValue(mockSharedPrefsService),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('userDatasourceProvider', () {
      test(
        'should create UserLocalDatasource with provided StorageService',
        () {
          // Act
          final datasource = container.read(
            userDatasourceProvider(mockStorageService),
          );

          // Assert
          expect(datasource, isA<UserLocalDatasource>());
          expect(datasource, isA<UserDataSource>());

          // Verify the datasource has the correct storage service
          final localDatasource = datasource as UserLocalDatasource;
          expect(localDatasource.storageService, equals(mockStorageService));
        },
      );

      test(
        'should create different instances for different StorageService instances',
        () {
          // Arrange
          final anotherMockStorage = MockFactories.createMockWithFallbacks(
            MockStorageService(),
          );
          when(() => anotherMockStorage.init()).thenReturn(null);
          when(() => anotherMockStorage.hasInitialized).thenReturn(true);

          // Act
          final datasource1 = container.read(
            userDatasourceProvider(mockStorageService),
          );
          final datasource2 = container.read(
            userDatasourceProvider(anotherMockStorage),
          );

          // Assert
          expect(datasource1, isNot(equals(datasource2)));
          expect(
            (datasource1 as UserLocalDatasource).storageService,
            equals(mockStorageService),
          );
          expect(
            (datasource2 as UserLocalDatasource).storageService,
            equals(anotherMockStorage),
          );
        },
      );

      test('should have correct storage key', () {
        // Act
        final datasource = container.read(
          userDatasourceProvider(mockStorageService),
        );

        // Assert
        expect(datasource.storageKey, isNotEmpty);
        expect(datasource.storageKey, equals('user'));
      });

      test('should handle different storage service types', () {
        // This test verifies that the provider works with different StorageService implementations
        final anotherStorageService = MockFactories.createMockWithFallbacks(
          MockStorageService(),
        );
        when(() => anotherStorageService.init()).thenReturn(null);
        when(() => anotherStorageService.hasInitialized).thenReturn(true);

        // Act
        final datasource = container.read(
          userDatasourceProvider(anotherStorageService),
        );

        // Assert
        expect(datasource, isA<UserLocalDatasource>());
        expect(
          (datasource as UserLocalDatasource).storageService,
          equals(anotherStorageService),
        );
      });
    });

    group('userLocalRepositoryProvider', () {
      test('should create UserRepositoryImpl with correct dependencies', () {
        // Act
        final repository = container.read(userLocalRepositoryProvider);

        // Assert
        expect(repository, isA<UserRepositoryImpl>());
        expect(repository, isA<UserRepository>());

        // Verify the repository has the correct datasource
        final implRepository = repository as UserRepositoryImpl;
        expect(implRepository.dataSource, isA<UserLocalDatasource>());
      });

      test('should use the storage service from the container', () {
        // Act
        final repository = container.read(userLocalRepositoryProvider);
        final datasource = (repository as UserRepositoryImpl).dataSource;
        final storageService =
            (datasource as UserLocalDatasource).storageService;

        // Assert
        expect(storageService, equals(mockSharedPrefsService));
      });

      test('should create singleton instance within same container', () {
        // Act
        final repository1 = container.read(userLocalRepositoryProvider);
        final repository2 = container.read(userLocalRepositoryProvider);

        // Assert
        expect(repository1, equals(repository2));
        expect(identical(repository1, repository2), isTrue);
      });

      test('should create different instances in different containers', () {
        // Arrange
        final anotherContainer = ProviderContainer(
          overrides: [
            storageServiceProvider.overrideWithValue(mockSharedPrefsService),
          ],
        );

        // Act
        final repository1 = container.read(userLocalRepositoryProvider);
        final repository2 = anotherContainer.read(userLocalRepositoryProvider);

        // Assert
        expect(repository1, isNot(equals(repository2)));
        expect(identical(repository1, repository2), isFalse);

        // Cleanup
        anotherContainer.dispose();
      });
    });

    group('Provider Integration Tests', () {
      test('should properly wire dependencies through the provider chain', () {
        // Act
        final repository = container.read(userLocalRepositoryProvider);
        final datasource = (repository as UserRepositoryImpl).dataSource;
        final storageService =
            (datasource as UserLocalDatasource).storageService;

        // Assert
        expect(repository, isA<UserRepository>());
        expect(datasource, isA<UserDataSource>());
        expect(storageService, isA<StorageService>());
        expect(storageService, equals(mockSharedPrefsService));
      });

      test('should handle storage service initialization', () {
        // Arrange
        when(() => mockSharedPrefsService.hasInitialized).thenReturn(false);

        // Act
        final repository = container.read(userLocalRepositoryProvider);

        // Assert
        expect(repository, isA<UserRepository>());
        // Note: hasInitialized is not called during repository creation
      });

      test('should work with overridden storage service', () {
        // Arrange
        final customSharedPrefsService = MockFactories.createMockWithFallbacks(
          MockSharedPrefsService(),
        );
        when(() => customSharedPrefsService.init()).thenReturn(null);
        when(() => customSharedPrefsService.hasInitialized).thenReturn(true);

        final customContainer = ProviderContainer(
          overrides: [
            storageServiceProvider.overrideWithValue(customSharedPrefsService),
          ],
        );

        // Act
        final repository = customContainer.read(userLocalRepositoryProvider);
        final datasource = (repository as UserRepositoryImpl).dataSource;
        final storageService =
            (datasource as UserLocalDatasource).storageService;

        // Assert
        expect(storageService, equals(customSharedPrefsService));
        expect(storageService, isNot(equals(mockSharedPrefsService)));

        // Cleanup
        customContainer.dispose();
      });
    });

    group('Error Handling Tests', () {
      test('should handle different storage service types', () {
        // This test verifies that the provider works with different StorageService implementations
        final anotherStorageService = MockFactories.createMockWithFallbacks(
          MockStorageService(),
        );
        when(() => anotherStorageService.init()).thenReturn(null);
        when(() => anotherStorageService.hasInitialized).thenReturn(true);

        // Act
        final datasource = container.read(
          userDatasourceProvider(anotherStorageService),
        );

        // Assert
        expect(datasource, isA<UserLocalDatasource>());
        expect(
          (datasource as UserLocalDatasource).storageService,
          equals(anotherStorageService),
        );
      });
    });

    group('Mock Verification Tests', () {
      test(
        'should verify storage service interactions during provider creation',
        () {
          // Act
          container.read(userLocalRepositoryProvider);

          // Assert - hasInitialized is not called during repository creation
          // The repository creation doesn't directly access storage service properties
          verifyNever(() => mockSharedPrefsService.hasInitialized);
        },
      );

      test('should not call storage service methods unnecessarily', () {
        // Act - Just read the provider without using it
        container.read(userLocalRepositoryProvider);

        // Assert - No storage service methods should be called during creation
        verifyNever(() => mockSharedPrefsService.get(any()));
        verifyNever(() => mockSharedPrefsService.set(any(), any()));
        verifyNever(() => mockSharedPrefsService.remove(any()));
        verifyNever(() => mockSharedPrefsService.has(any()));
        verifyNever(() => mockSharedPrefsService.clear());
        verifyNever(() => mockSharedPrefsService.hasInitialized);
      });
    });

    group('Provider Disposal Tests', () {
      test('should handle container disposal gracefully', () {
        // Act
        final repository = container.read(userLocalRepositoryProvider);

        // Assert - Repository should be created successfully
        expect(repository, isA<UserRepository>());

        // Act - Dispose container
        container.dispose();

        // Assert - Container should be disposed
        expect(
          () => container.read(userLocalRepositoryProvider),
          throwsStateError,
        );
      });
    });

    group('Riverpod Test Utils Integration', () {
      test('should work with RiverpodTestUtils.createTestContainer', () {
        // Act
        final testContainer = RiverpodTestUtils.createTestContainer(
          additionalOverrides: [
            storageServiceProvider.overrideWithValue(mockSharedPrefsService),
          ],
        );

        final repository = testContainer.read(userLocalRepositoryProvider);

        // Assert
        expect(repository, isA<UserRepository>());

        // Cleanup
        testContainer.dispose();
      });

      test('should support provider override testing', () {
        // Arrange
        final customRepository = MockFactories.createMockWithFallbacks(
          MockUserRepository(),
        );

        // Act
        final testContainer = ProviderContainer(
          overrides: [
            storageServiceProvider.overrideWithValue(mockSharedPrefsService),
            userLocalRepositoryProvider.overrideWithValue(customRepository),
          ],
        );

        final repository = testContainer.read(userLocalRepositoryProvider);

        // Assert
        expect(repository, equals(customRepository));

        // Cleanup
        testContainer.dispose();
      });
    });
  });
}
