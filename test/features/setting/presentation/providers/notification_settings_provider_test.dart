import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/features/setting/presentation/providers/notification_settings_provider.dart';
import 'package:selfeng/services/notification_service/domain/repositories/notification_service_repository.dart';
import 'package:selfeng/services/notification_service/domain/providers/notification_service_provider.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Mock classes for testing
class MockNotificationServiceRepository extends Mock
    implements NotificationServiceRepository {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('NotificationSettingsNotifier Tests', () {
    late MockNotificationServiceRepository mockNotificationService;
    late NotificationSettingsNotifier notifier;

    setUp(() async {
      mockNotificationService = MockNotificationServiceRepository();

      // Setup SharedPreferences with mock initial values BEFORE creating notifier
      SharedPreferences.setMockInitialValues({
        'notification_general_notification': true,
        'notification_promotion': true,
        'notification_announcement': true,
        'notification_study_reminder': true,
      });

      notifier = NotificationSettingsNotifier(mockNotificationService);
    });

    tearDown(() {
      reset(mockNotificationService);
    });

    group('initialization', () {
      test('should initialize with default values', () async {
        // Assert - should have default state (all true)
        expect(notifier.state['general_notification'], true);
        expect(notifier.state['promotion'], true);
        expect(notifier.state['announcement'], true);
        expect(notifier.state['study_reminder'], true);
      });

      test(
        'should initialize with custom values from SharedPreferences',
        () async {
          // Arrange - set different initial values
          SharedPreferences.setMockInitialValues({
            'notification_general_notification': false,
            'notification_promotion': true,
            'notification_announcement': false,
            'notification_study_reminder': true,
          });

          // Act - create new notifier with fresh SharedPreferences
          final newNotifier = NotificationSettingsNotifier(
            mockNotificationService,
          );

          // Wait for initialization
          await Future.delayed(Duration.zero);

          // Assert
          expect(newNotifier.state['general_notification'], false);
          expect(newNotifier.state['promotion'], true);
          expect(newNotifier.state['announcement'], false);
          expect(newNotifier.state['study_reminder'], true);
        },
      );
    });

    group('toggleNotification', () {
      test('should toggle notification successfully', () async {
        // Arrange
        const topic = 'general_notification';
        when(
          () => mockNotificationService.unsubscribeFromTopic(topic),
        ).thenAnswer((_) async => const Right(null));

        // Act
        await notifier.toggleNotification(topic);

        // Assert
        expect(notifier.state[topic], false);
        verify(
          () => mockNotificationService.unsubscribeFromTopic(topic),
        ).called(1);
      });

      test('should revert state when notification service fails', () async {
        // Arrange
        const topic = 'general_notification';
        when(
          () => mockNotificationService.unsubscribeFromTopic(topic),
        ).thenAnswer((_) async => throw Exception('Service error'));

        // Act
        await notifier.toggleNotification(topic);

        // Assert - should revert to original state
        expect(notifier.state[topic], true);
        verify(
          () => mockNotificationService.unsubscribeFromTopic(topic),
        ).called(1);
      });

      test('should do nothing for invalid topic', () async {
        // Act
        await notifier.toggleNotification('invalid_topic');

        // Assert - state unchanged
        expect(notifier.state.length, 4);
        verifyNever(() => mockNotificationService.subscribeToTopic(any()));
        verifyNever(() => mockNotificationService.unsubscribeFromTopic(any()));
      });
    });

    group('enableAllNotifications', () {
      test('should enable all notifications successfully', () async {
        // Arrange
        when(
          () =>
              mockNotificationService.subscribeToTopic('general_notification'),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.subscribeToTopic('promotion'),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.subscribeToTopic('announcement'),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.subscribeToTopic('study_reminder'),
        ).thenAnswer((_) async => const Right(null));

        // Act
        await notifier.enableAllNotifications();

        // Assert
        expect(notifier.state.values.every((enabled) => enabled), true);
        verify(
          () =>
              mockNotificationService.subscribeToTopic('general_notification'),
        ).called(1);
        verify(
          () => mockNotificationService.subscribeToTopic('promotion'),
        ).called(1);
        verify(
          () => mockNotificationService.subscribeToTopic('announcement'),
        ).called(1);
        verify(
          () => mockNotificationService.subscribeToTopic('study_reminder'),
        ).called(1);
      });
    });

    group('disableAllNotifications', () {
      test('should disable all notifications successfully', () async {
        // Arrange
        when(
          () => mockNotificationService.unsubscribeFromTopic(
            'general_notification',
          ),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.unsubscribeFromTopic('promotion'),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.unsubscribeFromTopic('announcement'),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.unsubscribeFromTopic('study_reminder'),
        ).thenAnswer((_) async => const Right(null));

        // Act
        await notifier.disableAllNotifications();

        // Assert
        expect(notifier.state.values.any((enabled) => enabled), false);
        verify(
          () => mockNotificationService.unsubscribeFromTopic(
            'general_notification',
          ),
        ).called(1);
        verify(
          () => mockNotificationService.unsubscribeFromTopic('promotion'),
        ).called(1);
        verify(
          () => mockNotificationService.unsubscribeFromTopic('announcement'),
        ).called(1);
        verify(
          () => mockNotificationService.unsubscribeFromTopic('study_reminder'),
        ).called(1);
      });
    });

    group('isNotificationEnabled', () {
      test('should return true for enabled notification', () {
        // Act & Assert
        expect(notifier.isNotificationEnabled('general_notification'), true);
      });

      test('should return false for disabled notification', () async {
        // Arrange
        const topic = 'general_notification';
        when(
          () => mockNotificationService.unsubscribeFromTopic(topic),
        ).thenAnswer((_) async => const Right(null));

        await notifier.toggleNotification(topic);

        // Act & Assert
        expect(notifier.isNotificationEnabled(topic), false);
      });

      test('should return false for invalid topic', () {
        // Act & Assert
        expect(notifier.isNotificationEnabled('invalid_topic'), false);
      });
    });

    group('initializeSubscriptions', () {
      test('should subscribe to enabled notifications', () async {
        // Arrange
        when(
          () =>
              mockNotificationService.subscribeToTopic('general_notification'),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.subscribeToTopic('promotion'),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.subscribeToTopic('announcement'),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.subscribeToTopic('study_reminder'),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.unsubscribeFromTopic(any()),
        ).thenAnswer((_) async => const Right(null));

        // Act
        await notifier.initializeSubscriptions();

        // Assert
        verify(
          () =>
              mockNotificationService.subscribeToTopic('general_notification'),
        ).called(1);
        verify(
          () => mockNotificationService.subscribeToTopic('promotion'),
        ).called(1);
        verify(
          () => mockNotificationService.subscribeToTopic('announcement'),
        ).called(1);
        verify(
          () => mockNotificationService.subscribeToTopic('study_reminder'),
        ).called(1);
        verifyNever(() => mockNotificationService.unsubscribeFromTopic(any()));
      });

      test('should handle service errors silently', () async {
        // Arrange
        when(
          () => mockNotificationService.subscribeToTopic(any()),
        ).thenThrow(Exception('Service error'));

        // Act - should not throw
        await notifier.initializeSubscriptions();

        // Assert - no exceptions, continues silently
        verify(
          () =>
              mockNotificationService.subscribeToTopic('general_notification'),
        ).called(1);
      });
    });
  });

  group('Notification Settings Providers Tests', () {
    late ProviderContainer container;
    late MockNotificationServiceRepository mockNotificationService;

    setUp(() async {
      mockNotificationService = MockNotificationServiceRepository();

      // Setup SharedPreferences with mock initial values BEFORE creating container
      SharedPreferences.setMockInitialValues({
        'notification_general_notification': true,
        'notification_promotion': true,
        'notification_announcement': true,
        'notification_study_reminder': true,
      });

      container = ProviderContainer(
        overrides: [
          notificationServiceProvider.overrideWithValue(
            mockNotificationService,
          ),
        ],
      );
    });

    tearDown(() {
      container.dispose();
      reset(mockNotificationService);
    });

    test(
      'notificationSettingsProvider should provide NotificationSettingsNotifier',
      () {
        // Act
        final notifier = container.read(notificationSettingsProvider.notifier);

        // Assert
        expect(notifier, isA<NotificationSettingsNotifier>());
      },
    );

    test('notificationSettingsProvider should provide initial state', () {
      // Act
      final state = container.read(notificationSettingsProvider);

      // Assert
      expect(state.length, 4);
      expect(state['general_notification'], true);
      expect(state['promotion'], true);
      expect(state['announcement'], true);
      expect(state['study_reminder'], true);
    });

    test(
      'notificationEnabledProvider should return enabled status for topic',
      () {
        // Act
        final isEnabled = container.read(
          notificationEnabledProvider('general_notification'),
        );

        // Assert
        expect(isEnabled, true);
      },
    );

    test(
      'notificationEnabledProvider should return false for invalid topic',
      () {
        // Act
        final isEnabled = container.read(
          notificationEnabledProvider('invalid_topic'),
        );

        // Assert
        expect(isEnabled, false);
      },
    );

    test(
      'allNotificationsEnabledProvider should return true when all enabled',
      () {
        // Act
        final allEnabled = container.read(allNotificationsEnabledProvider);

        // Assert
        expect(allEnabled, true);
      },
    );

    test(
      'allNotificationsEnabledProvider should return false when some disabled',
      () async {
        // Arrange
        final notifier = container.read(notificationSettingsProvider.notifier);
        const topic = 'general_notification';
        when(
          () => mockNotificationService.unsubscribeFromTopic(topic),
        ).thenAnswer((_) async => const Right(null));

        await notifier.toggleNotification(topic);

        // Act
        final allEnabled = container.read(allNotificationsEnabledProvider);

        // Assert
        expect(allEnabled, false);
      },
    );

    test(
      'anyNotificationEnabledProvider should return true when at least one enabled',
      () {
        // Act
        final anyEnabled = container.read(anyNotificationEnabledProvider);

        // Assert
        expect(anyEnabled, true);
      },
    );

    test(
      'anyNotificationEnabledProvider should return false when all disabled',
      () async {
        // Arrange
        final notifier = container.read(notificationSettingsProvider.notifier);

        // Setup mocks for all unsubscribe calls
        when(
          () => mockNotificationService.unsubscribeFromTopic(
            'general_notification',
          ),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.unsubscribeFromTopic('promotion'),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.unsubscribeFromTopic('announcement'),
        ).thenAnswer((_) async => const Right(null));
        when(
          () => mockNotificationService.unsubscribeFromTopic('study_reminder'),
        ).thenAnswer((_) async => const Right(null));

        // Set up fresh SharedPreferences with disabled values to prevent _loadSettings from overriding
        SharedPreferences.setMockInitialValues({
          'notification_general_notification': false,
          'notification_promotion': false,
          'notification_announcement': false,
          'notification_study_reminder': false,
        });

        await notifier.disableAllNotifications();

        // Act
        final anyEnabled = container.read(anyNotificationEnabledProvider);

        // Assert
        expect(anyEnabled, false);
      },
    );
  });
}
