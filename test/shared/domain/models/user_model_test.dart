import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/models/user/user_model.dart';
import '../../../helpers/test_data_builders.dart';

void main() {
  group('User Model Tests', () {
    group('Constructor and Properties', () {
      test('should create User with default values', () {
        const user = User();

        expect(user.id, equals(0));
        expect(user.username, equals(''));
        expect(user.password, equals(''));
        expect(user.email, equals(''));
        expect(user.token_fcm, equals(''));
        expect(user.firstName, equals(''));
        expect(user.lastName, equals(''));
        expect(user.gender, equals(''));
        expect(user.image, equals(''));
        expect(user.token, equals(''));
      });

      test('should create User with custom values', () {
        final user = User(
          id: 123,
          username: 'johndoe',
          password: 'password123',
          email: '<EMAIL>',
          token_fcm: 'fcm_token_123',
          firstName: 'John',
          lastName: 'Doe',
          gender: 'male',
          image: 'profile.jpg',
          token: 'auth_token_123',
        );

        expect(user.id, equals(123));
        expect(user.username, equals('johndoe'));
        expect(user.password, equals('password123'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.token_fcm, equals('fcm_token_123'));
        expect(user.firstName, equals('John'));
        expect(user.lastName, equals('Doe'));
        expect(user.gender, equals('male'));
        expect(user.image, equals('profile.jpg'));
        expect(user.token, equals('auth_token_123'));
      });

      test('should create User with partial values', () {
        final user = User(
          id: 456,
          email: '<EMAIL>',
          firstName: 'Test',
        );

        expect(user.id, equals(456));
        expect(user.email, equals('<EMAIL>'));
        expect(user.firstName, equals('Test'));
        // Other fields should have default values
        expect(user.username, equals(''));
        expect(user.password, equals(''));
        expect(user.token_fcm, equals(''));
        expect(user.lastName, equals(''));
        expect(user.gender, equals(''));
        expect(user.image, equals(''));
        expect(user.token, equals(''));
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final user =
            TestDataBuilders.userBuilder()
                .withId(123)
                .withUsername('johndoe')
                .withEmail('<EMAIL>')
                .withFirstName('John')
                .withLastName('Doe')
                .withPassword('')
                .withTokenFcm('')
                .withGender('')
                .withImage('')
                .withToken('')
                .build();

        final json = user.toJson();

        expect(json['id'], equals(123));
        expect(json['username'], equals('johndoe'));
        expect(json['password'], equals(''));
        expect(json['email'], equals('<EMAIL>'));
        expect(json['token_fcm'], equals(''));
        expect(json['firstName'], equals('John'));
        expect(json['lastName'], equals('Doe'));
        expect(json['gender'], equals(''));
        expect(json['image'], equals(''));
        expect(json['token'], equals(''));
      });

      test('should serialize with all fields populated', () {
        final user =
            TestDataBuilders.userBuilder().withCompleteProfile().build();

        final json = user.toJson();

        expect(json['id'], equals(123));
        expect(json['username'], equals('johndoe'));
        expect(json['password'], equals('secure_password'));
        expect(json['email'], equals('<EMAIL>'));
        expect(json['token_fcm'], equals('fcm_token_complete'));
        expect(json['firstName'], equals('John'));
        expect(json['lastName'], equals('Doe'));
        expect(json['gender'], equals('male'));
        expect(json['image'], equals('john_profile.jpg'));
        expect(json['token'], equals('auth_token_complete'));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'id': 789,
          'username': 'janedoe',
          'password': 'password456',
          'email': '<EMAIL>',
          'token_fcm': 'fcm_token_456',
          'firstName': 'Jane',
          'lastName': 'Doe',
          'gender': 'female',
          'image': 'jane_profile.jpg',
          'token': 'auth_token_456',
        };

        final user = User.fromJson(json);

        expect(user.id, equals(789));
        expect(user.username, equals('janedoe'));
        expect(user.password, equals('password456'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.token_fcm, equals('fcm_token_456'));
        expect(user.firstName, equals('Jane'));
        expect(user.lastName, equals('Doe'));
        expect(user.gender, equals('female'));
        expect(user.image, equals('jane_profile.jpg'));
        expect(user.token, equals('auth_token_456'));
      });

      test('should handle missing optional fields in JSON', () {
        final json = {'id': 999, 'email': '<EMAIL>'};

        final user = User.fromJson(json);

        expect(user.id, equals(999));
        expect(user.email, equals('<EMAIL>'));
        // Missing fields should use defaults
        expect(user.username, equals(''));
        expect(user.password, equals(''));
        expect(user.token_fcm, equals(''));
        expect(user.firstName, equals(''));
        expect(user.lastName, equals(''));
        expect(user.gender, equals(''));
        expect(user.image, equals(''));
        expect(user.token, equals(''));
      });

      test('should handle null values in JSON by using defaults', () {
        final json = {
          'id': null,
          'username': null,
          'password': null,
          'email': null,
          'token_fcm': null,
          'firstName': null,
          'lastName': null,
          'gender': null,
          'image': null,
          'token': null,
        };

        final user = User.fromJson(json);

        expect(user.id, equals(0));
        expect(user.username, equals(''));
        expect(user.password, equals(''));
        expect(user.email, equals(''));
        expect(user.token_fcm, equals(''));
        expect(user.firstName, equals(''));
        expect(user.lastName, equals(''));
        expect(user.gender, equals(''));
        expect(user.image, equals(''));
        expect(user.token, equals(''));
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        final user1 =
            TestDataBuilders.userBuilder()
                .withId(123)
                .withUsername('testuser')
                .withEmail('<EMAIL>')
                .withFirstName('Test')
                .build();

        final user2 =
            TestDataBuilders.userBuilder()
                .withId(123)
                .withUsername('testuser')
                .withEmail('<EMAIL>')
                .withFirstName('Test')
                .build();

        expect(user1, equals(user2));
        expect(user1.hashCode, equals(user2.hashCode));
      });

      test('should not be equal when id is different', () {
        final user1 = TestDataBuilders.userBuilder().withId(123).build();

        final user2 = TestDataBuilders.userBuilder().withId(456).build();

        expect(user1, isNot(equals(user2)));
        expect(user1.hashCode, isNot(equals(user2.hashCode)));
      });

      test('should not be equal when username is different', () {
        final user1 =
            TestDataBuilders.userBuilder().withUsername('user1').build();

        final user2 =
            TestDataBuilders.userBuilder().withUsername('user2').build();

        expect(user1, isNot(equals(user2)));
        expect(user1.hashCode, isNot(equals(user2.hashCode)));
      });

      test('should not be equal when email is different', () {
        final user1 =
            TestDataBuilders.userBuilder()
                .withEmail('<EMAIL>')
                .build();

        final user2 =
            TestDataBuilders.userBuilder()
                .withEmail('<EMAIL>')
                .build();

        expect(user1, isNot(equals(user2)));
        expect(user1.hashCode, isNot(equals(user2.hashCode)));
      });

      test('should not be equal when firstName is different', () {
        final user1 =
            TestDataBuilders.userBuilder().withFirstName('John').build();

        final user2 =
            TestDataBuilders.userBuilder().withFirstName('Jane').build();

        expect(user1, isNot(equals(user2)));
        expect(user1.hashCode, isNot(equals(user2.hashCode)));
      });

      test('should not be equal when lastName is different', () {
        final user1 =
            TestDataBuilders.userBuilder().withLastName('Doe').build();

        final user2 =
            TestDataBuilders.userBuilder().withLastName('Smith').build();

        expect(user1, isNot(equals(user2)));
        expect(user1.hashCode, isNot(equals(user2.hashCode)));
      });
    });

    group('CopyWith Method', () {
      test('should create copy with updated id', () {
        final original =
            TestDataBuilders.userBuilder()
                .withId(123)
                .withEmail('<EMAIL>')
                .build();

        final updated = original.copyWith(id: 456);

        expect(updated.id, equals(456));
        expect(updated.email, equals(original.email));
        expect(updated.username, equals(original.username));
        expect(updated.firstName, equals(original.firstName));
        expect(updated.lastName, equals(original.lastName));
      });

      test('should create copy with updated email', () {
        final original =
            TestDataBuilders.userBuilder()
                .withEmail('<EMAIL>')
                .build();

        final updated = original.copyWith(email: '<EMAIL>');

        expect(updated.email, equals('<EMAIL>'));
        expect(updated.id, equals(original.id));
        expect(updated.username, equals(original.username));
        expect(updated.firstName, equals(original.firstName));
        expect(updated.lastName, equals(original.lastName));
      });

      test('should create copy with updated username', () {
        final original =
            TestDataBuilders.userBuilder().withUsername('originaluser').build();

        final updated = original.copyWith(username: 'updateduser');

        expect(updated.username, equals('updateduser'));
        expect(updated.id, equals(original.id));
        expect(updated.email, equals(original.email));
        expect(updated.firstName, equals(original.firstName));
        expect(updated.lastName, equals(original.lastName));
      });

      test('should create copy with updated firstName', () {
        final original =
            TestDataBuilders.userBuilder().withFirstName('John').build();

        final updated = original.copyWith(firstName: 'Jane');

        expect(updated.firstName, equals('Jane'));
        expect(updated.id, equals(original.id));
        expect(updated.email, equals(original.email));
        expect(updated.username, equals(original.username));
        expect(updated.lastName, equals(original.lastName));
      });

      test('should create copy with updated lastName', () {
        final original =
            TestDataBuilders.userBuilder().withLastName('Doe').build();

        final updated = original.copyWith(lastName: 'Smith');

        expect(updated.lastName, equals('Smith'));
        expect(updated.id, equals(original.id));
        expect(updated.email, equals(original.email));
        expect(updated.username, equals(original.username));
        expect(updated.firstName, equals(original.firstName));
      });

      test('should create copy with multiple updated fields', () {
        final original =
            TestDataBuilders.userBuilder()
                .withId(123)
                .withEmail('<EMAIL>')
                .withFirstName('John')
                .withLastName('Doe')
                .build();

        final updated = original.copyWith(
          id: 456,
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Smith',
        );

        expect(updated.id, equals(456));
        expect(updated.email, equals('<EMAIL>'));
        expect(updated.firstName, equals('Jane'));
        expect(updated.lastName, equals('Smith'));
        expect(updated.username, equals(original.username)); // Unchanged
      });

      test('should return equal instance when no changes are made', () {
        final original = TestDataBuilders.userBuilder().build();

        final updated = original.copyWith();

        expect(updated, equals(original));
        expect(updated.hashCode, equals(original.hashCode));
      });
    });

    group('ToString Method', () {
      test('should provide readable string representation', () {
        final user =
            TestDataBuilders.userBuilder()
                .withId(123)
                .withUsername('johndoe')
                .withEmail('<EMAIL>')
                .withFirstName('John')
                .withLastName('Doe')
                .build();

        final stringRepresentation = user.toString();

        expect(stringRepresentation, contains('User'));
        expect(stringRepresentation, contains('123'));
        expect(stringRepresentation, contains('johndoe'));
        expect(stringRepresentation, contains('<EMAIL>'));
        expect(stringRepresentation, contains('John'));
        expect(stringRepresentation, contains('Doe'));
      });

      test('should handle default values in toString', () {
        const user = User();

        final stringRepresentation = user.toString();

        expect(stringRepresentation, contains('User'));
        expect(stringRepresentation, contains('id: 0'));
        expect(stringRepresentation, contains('username: '));
        expect(stringRepresentation, contains('email: '));
        expect(stringRepresentation, contains('firstName: '));
        expect(stringRepresentation, contains('lastName: '));
      });
    });

    group('Edge Cases', () {
      test('should handle empty strings', () {
        final user = User(
          id: 0,
          username: '',
          password: '',
          email: '',
          token_fcm: '',
          firstName: '',
          lastName: '',
          gender: '',
          image: '',
          token: '',
        );

        expect(user.username, equals(''));
        expect(user.email, equals(''));
        expect(user.firstName, equals(''));
        expect(user.lastName, equals(''));
      });

      test('should handle very long strings', () {
        final longString = 'a' * 1000;
        final user = User(
          username: longString,
          email: longString,
          firstName: longString,
          lastName: longString,
        );

        expect(user.username.length, equals(1000));
        expect(user.email.length, equals(1000));
        expect(user.firstName.length, equals(1000));
        expect(user.lastName.length, equals(1000));
      });

      test('should handle special characters', () {
        final user = User(
          username: 'user@test',
          email: '<EMAIL>',
          firstName: 'José María',
          lastName: 'O\'Connor-Smith',
        );

        expect(user.username, equals('user@test'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.firstName, equals('José María'));
        expect(user.lastName, equals('O\'Connor-Smith'));
      });

      test('should handle unicode characters', () {
        final user = User(firstName: '张三', lastName: '李四', username: '用户123');

        expect(user.firstName, equals('张三'));
        expect(user.lastName, equals('李四'));
        expect(user.username, equals('用户123'));
      });

      test('should serialize and deserialize consistently', () {
        final original =
            TestDataBuilders.userBuilder().withCompleteProfile().build();

        final json = original.toJson();
        final deserialized = User.fromJson(json);

        expect(deserialized, equals(original));
        expect(deserialized.id, equals(original.id));
        expect(deserialized.username, equals(original.username));
        expect(deserialized.email, equals(original.email));
        expect(deserialized.firstName, equals(original.firstName));
        expect(deserialized.lastName, equals(original.lastName));
      });

      test('should handle zero id', () {
        final user = User(id: 0);

        expect(user.id, equals(0));
        expect(user.id, isA<int>());
      });

      test('should handle negative id', () {
        final user = User(id: -123);

        expect(user.id, equals(-123));
        expect(user.id, isNegative);
      });

      test('should handle large id', () {
        final user = User(id: 999999999);

        expect(user.id, equals(999999999));
        expect(user.id, greaterThan(999999998));
      });
    });

    group('Freezed-Specific Features', () {
      test('should support pattern matching with when', () {
        final user =
            TestDataBuilders.userBuilder()
                .withId(123)
                .withEmail('<EMAIL>')
                .build();

        final result = user.when((
          id,
          username,
          password,
          email,
          tokenFcm,
          firstName,
          lastName,
          gender,
          image,
          token,
        ) {
          return 'User: $firstName $lastName ($email)';
        });

        expect(result, equals('User: John Doe (<EMAIL>)'));
      });

      test('should support pattern matching with maybeWhen', () {
        final user =
            TestDataBuilders.userBuilder()
                .withId(123)
                .withEmail('<EMAIL>')
                .build();

        final result = user.maybeWhen(orElse: () => 'Unknown', (
          id,
          username,
          password,
          email,
          tokenFcm,
          firstName,
          lastName,
          gender,
          image,
          token,
        ) {
          return 'User: $firstName $lastName';
        });

        expect(result, equals('User: John Doe'));
      });

      test('should support map method', () {
        final user = TestDataBuilders.userBuilder().build();

        final result = user.map((user) => 'Mapped: ${user.firstName}');

        expect(result, equals('Mapped: John'));
      });

      test('should support maybeMap method', () {
        final user = TestDataBuilders.userBuilder().build();

        final result = user.maybeMap(
          orElse: () => 'Unknown',
          (user) => 'Maybe Mapped: ${user.firstName}',
        );

        expect(result, equals('Maybe Mapped: John'));
      });
    });
  });
}
