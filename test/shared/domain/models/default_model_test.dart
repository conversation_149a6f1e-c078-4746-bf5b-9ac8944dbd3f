import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/models/default_model/default_model.dart';

void main() {
  group('DefaultModel Tests', () {
    group('Factory Constructor and Default Values', () {
      test('should create instance with default values', () {
        // Act
        final model = DefaultModel();

        // Assert
        expect(model.title, equals(''));
        expect(model.subtitle, equals(''));
        expect(model.image, equals(''));
        expect(model.route, equals(''));
        expect(model.isExpanded, isFalse);
        expect(model.params, isNull);
        expect(model.child, isNull);
        expect(model.onTap, isNull);
      });

      test('should create instance with custom values', () {
        // Arrange
        const title = 'Test Title';
        const subtitle = 'Test Subtitle';
        const image = 'test_image.png';
        const route = '/test/route';
        const isExpanded = true;
        final params = {'key1': 'value1', 'key2': 'value2'};
        final child = 'mock_container';
        void onTap() {}

        // Act
        final model = DefaultModel(
          title: title,
          subtitle: subtitle,
          image: image,
          route: route,
          isExpanded: isExpanded,
          params: params,
          child: child,
          onTap: onTap,
        );

        // Assert
        expect(model.title, equals(title));
        expect(model.subtitle, equals(subtitle));
        expect(model.image, equals(image));
        expect(model.route, equals(route));
        expect(model.isExpanded, equals(isExpanded));
        expect(model.params, equals(params));
        expect(model.child, equals(child));
        expect(model.onTap, equals(onTap));
      });

      test('should handle partial custom values with defaults', () {
        // Act
        final model = DefaultModel(title: 'Custom Title', isExpanded: true);

        // Assert
        expect(model.title, equals('Custom Title'));
        expect(model.subtitle, equals('')); // default
        expect(model.image, equals('')); // default
        expect(model.route, equals('')); // default
        expect(model.isExpanded, isTrue); // custom
        expect(model.params, isNull); // default
        expect(model.child, isNull); // default
        expect(model.onTap, isNull); // default
      });
    });

    group('JSON Serialization and Deserialization', () {
      test('should serialize to JSON correctly', () {
        // Arrange
        final model = DefaultModel(
          title: 'Test Title',
          subtitle: 'Test Subtitle',
          image: 'test_image.png',
          route: '/test/route',
          isExpanded: true,
          params: {'key1': 'value1', 'key2': 'value2'},
          child: 'mock_container', // This should be excluded from JSON
          onTap: () {}, // This should be excluded from JSON
        );

        // Act
        final json = model.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['title'], equals('Test Title'));
        expect(json['subtitle'], equals('Test Subtitle'));
        expect(json['image'], equals('test_image.png'));
        expect(json['route'], equals('/test/route'));
        expect(json['isExpanded'], isTrue);
        expect(json['params'], equals({'key1': 'value1', 'key2': 'value2'}));

        // Excluded fields should not be in JSON
        expect(json.containsKey('child'), isFalse);
        // onTap is included in JSON (no @JsonKey annotation)
        expect(json.containsKey('onTap'), isTrue);
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final json = {
          'title': 'JSON Title',
          'subtitle': 'JSON Subtitle',
          'image': 'json_image.png',
          'route': '/json/route',
          'isExpanded': true,
          'params': {'jsonKey': 'jsonValue'},
        };

        // Act
        final model = DefaultModel.fromJson(json);

        // Assert
        expect(model.title, equals('JSON Title'));
        expect(model.subtitle, equals('JSON Subtitle'));
        expect(model.image, equals('json_image.png'));
        expect(model.route, equals('/json/route'));
        expect(model.isExpanded, isTrue);
        expect(model.params, equals({'jsonKey': 'jsonValue'}));

        // Excluded fields should be null
        expect(model.child, isNull);
        expect(model.onTap, isNull);
      });

      test('should handle JSON with missing optional fields', () {
        // Arrange
        final json = {'title': 'Minimal Title', 'isExpanded': false};

        // Act
        final model = DefaultModel.fromJson(json);

        // Assert
        expect(model.title, equals('Minimal Title'));
        expect(model.subtitle, equals('')); // default
        expect(model.image, equals('')); // default
        expect(model.route, equals('')); // default
        expect(model.isExpanded, isFalse);
        expect(model.params, isNull); // default
      });

      test('should handle empty JSON object', () {
        // Arrange
        final json = <String, dynamic>{};

        // Act
        final model = DefaultModel.fromJson(json);

        // Assert - All fields should have default values
        expect(model.title, equals(''));
        expect(model.subtitle, equals(''));
        expect(model.image, equals(''));
        expect(model.route, equals(''));
        expect(model.isExpanded, isFalse);
        expect(model.params, isNull);
      });

      test('should handle null JSON values gracefully', () {
        // Arrange
        final json = {
          'title': null,
          'subtitle': null,
          'image': null,
          'route': null,
          'isExpanded': null,
          'params': null,
        };

        // Act
        final model = DefaultModel.fromJson(json);

        // Assert - Should handle nulls gracefully with defaults
        expect(model.title, equals(''));
        expect(model.subtitle, equals(''));
        expect(model.image, equals(''));
        expect(model.route, equals(''));
        expect(model.isExpanded, isFalse);
        expect(model.params, isNull);
      });

      test(
        'should maintain data integrity through serialize/deserialize cycle',
        () {
          // Arrange
          final original = DefaultModel(
            title: 'Original Title',
            subtitle: 'Original Subtitle',
            image: 'original.png',
            route: '/original',
            isExpanded: true,
            params: {'original': 'value'},
            child: 'mock_text_widget', // excluded
            onTap: () {}, // test function
          );

          // Act
          final json = original.toJson();
          final deserialized = DefaultModel.fromJson(json);

          // Assert
          expect(deserialized.title, equals(original.title));
          expect(deserialized.subtitle, equals(original.subtitle));
          expect(deserialized.image, equals(original.image));
          expect(deserialized.route, equals(original.route));
          expect(deserialized.isExpanded, equals(original.isExpanded));
          expect(deserialized.params, equals(original.params));

          // Excluded fields should not be preserved
          expect(deserialized.child, isNull);
          // onTap is preserved since it's included in JSON
          expect(deserialized.onTap, equals(original.onTap));
        },
      );
    });

    group('copyWith Method', () {
      late DefaultModel original;

      setUp(() {
        original = DefaultModel(
          title: 'Original Title',
          subtitle: 'Original Subtitle',
          image: 'original.png',
          route: '/original',
          isExpanded: false,
          params: {'original': 'value'},
          child: 'mock_container',
          onTap: () {},
        );
      });

      test('should return identical instance when no changes', () {
        // Act
        final copy = original.copyWith();

        // Assert
        expect(copy, equals(original));
        expect(copy.hashCode, equals(original.hashCode));
      });

      test('should create copy with single field change', () {
        // Act
        final copy = original.copyWith(title: 'New Title');

        // Assert
        expect(copy.title, equals('New Title'));
        expect(copy.subtitle, equals(original.subtitle));
        expect(copy.image, equals(original.image));
        expect(copy.route, equals(original.route));
        expect(copy.isExpanded, equals(original.isExpanded));
        expect(copy.params, equals(original.params));
        expect(copy.child, equals(original.child));
        expect(copy.onTap, equals(original.onTap));
      });

      test('should create copy with multiple field changes', () {
        // Act
        final copy = original.copyWith(
          title: 'New Title',
          isExpanded: true,
          route: '/new/route',
        );

        // Assert
        expect(copy.title, equals('New Title'));
        expect(copy.isExpanded, isTrue);
        expect(copy.route, equals('/new/route'));

        // Unchanged fields should remain the same
        expect(copy.subtitle, equals(original.subtitle));
        expect(copy.image, equals(original.image));
        expect(copy.params, equals(original.params));
        expect(copy.child, equals(original.child));
        expect(copy.onTap, equals(original.onTap));
      });

      test('should handle null values in copyWith', () {
        // Act
        final copy = original.copyWith(params: null, child: null, onTap: null);

        // Assert
        expect(copy.params, isNull);
        expect(copy.child, isNull);
        expect(copy.onTap, isNull);

        // Other fields should remain unchanged
        expect(copy.title, equals(original.title));
        expect(copy.isExpanded, equals(original.isExpanded));
      });

      test('should create independent copies', () {
        // Act
        final copy1 = original.copyWith(title: 'Copy 1');
        final copy2 = original.copyWith(title: 'Copy 2');

        // Assert
        expect(copy1.title, equals('Copy 1'));
        expect(copy2.title, equals('Copy 2'));
        expect(original.title, equals('Original Title'));

        // Original should remain unchanged
        expect(original.title, isNot(equals(copy1.title)));
        expect(original.title, isNot(equals(copy2.title)));
      });
    });

    group('Equality and HashCode', () {
      test('should be equal when all properties are the same', () {
        // Arrange - Use the same function reference to ensure equality
        void onTapFunction() {}
        final model1 = DefaultModel(
          title: 'Test',
          subtitle: 'Subtitle',
          image: 'image.png',
          route: '/test',
          isExpanded: true,
          params: {'key': 'value'},
          child: 'mock_container',
          onTap: onTapFunction,
        );

        final model2 = DefaultModel(
          title: 'Test',
          subtitle: 'Subtitle',
          image: 'image.png',
          route: '/test',
          isExpanded: true,
          params: {'key': 'value'},
          child: 'mock_container',
          onTap: onTapFunction, // Same function reference
        );

        // Assert
        expect(model1, equals(model2));
        expect(model1.hashCode, equals(model2.hashCode));
      });

      test('should not be equal when any property differs', () {
        // Arrange
        final base = DefaultModel(
          title: 'Test',
          subtitle: 'Subtitle',
          image: 'image.png',
          route: '/test',
          isExpanded: true,
          params: {'key': 'value'},
        );

        final differentTitle = base.copyWith(title: 'Different');
        final differentExpanded = base.copyWith(isExpanded: false);
        final differentParams = base.copyWith(params: {'different': 'value'});

        // Assert
        expect(base, isNot(equals(differentTitle)));
        expect(base, isNot(equals(differentExpanded)));
        expect(base, isNot(equals(differentParams)));
      });

      test('should handle null values in equality', () {
        // Arrange
        final withNulls = DefaultModel(
          title: 'Test',
          params: null,
          child: null,
          onTap: null,
        );

        final alsoWithNulls = DefaultModel(
          title: 'Test',
          params: null,
          child: null,
          onTap: null,
        );

        // Assert
        expect(withNulls, equals(alsoWithNulls));
        expect(withNulls.hashCode, equals(alsoWithNulls.hashCode));
      });

      test('should not be equal to different types', () {
        // Arrange
        final model = DefaultModel(title: 'Test');
        final other = 'not a model';

        // Assert
        expect(model, isNot(equals(other)));
      });
    });

    group('Type Definitions and Collections', () {
      test('should work with DefaultModelList typedef', () {
        // Arrange
        final models = [
          DefaultModel(title: 'Model 1', route: '/1'),
          DefaultModel(title: 'Model 2', route: '/2'),
          DefaultModel(title: 'Model 3', route: '/3'),
        ];

        // Act
        final modelList = models;

        // Assert
        expect(modelList, isA<List<DefaultModel>>());
        expect(modelList.length, equals(3));
        expect(modelList[0].title, equals('Model 1'));
        expect(modelList[1].title, equals('Model 2'));
        expect(modelList[2].title, equals('Model 3'));
      });

      test('should handle empty DefaultModelList', () {
        // Arrange
        final emptyList = <DefaultModel>[];

        // Assert
        expect(emptyList, isA<DefaultModelList>());
        expect(emptyList, isEmpty);
      });

      test('should support collection operations', () {
        // Arrange
        final models = [
          DefaultModel(title: 'A', isExpanded: true),
          DefaultModel(title: 'B', isExpanded: false),
          DefaultModel(title: 'C', isExpanded: true),
        ];

        // Act
        final expandedModels =
            models.where((model) => model.isExpanded).toList();
        final titles = models.map((model) => model.title).toList();

        // Assert
        expect(expandedModels.length, equals(2));
        expect(titles, equals(['A', 'B', 'C']));
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle very long strings', () {
        // Arrange
        final longString = 'a' * 10000;

        // Act
        final model = DefaultModel(
          title: longString,
          subtitle: longString,
          image: longString,
          route: longString,
        );

        // Assert
        expect(model.title.length, equals(10000));
        expect(model.subtitle.length, equals(10000));
        expect(model.image.length, equals(10000));
        expect(model.route.length, equals(10000));
      });

      test('should handle special characters in strings', () {
        // Arrange
        const specialChars = '特殊字符 🚀 émojis 🎉 ñoños';

        // Act
        final model = DefaultModel(title: specialChars, subtitle: specialChars);

        // Assert
        expect(model.title, equals(specialChars));
        expect(model.subtitle, equals(specialChars));
      });

      test('should handle empty maps and collections', () {
        // Act
        final model = DefaultModel(params: {});

        // Assert
        expect(model.params, isNotNull);
        expect(model.params, isEmpty);
      });

      test('should handle large parameter maps', () {
        // Arrange
        final largeParams = Map<String, String>.fromEntries(
          List.generate(1000, (i) => MapEntry('key$i', 'value$i')),
        );

        // Act
        final model = DefaultModel(params: largeParams);

        // Assert
        expect(model.params?.length, equals(1000));
        expect(model.params?['key500'], equals('value500'));
      });
    });

    group('Integration with Test Data Builders', () {
      test('should work with test data builders pattern', () {
        // This test demonstrates integration with the test data builders
        // mentioned in the test guidelines

        // Act - Simulate using a builder pattern (if it existed)
        final model = DefaultModel(
          title: 'Builder Title',
          subtitle: 'Builder Subtitle',
          isExpanded: true,
        );

        // Assert
        expect(model.title, equals('Builder Title'));
        expect(model.subtitle, equals('Builder Subtitle'));
        expect(model.isExpanded, isTrue);
      });

      test('should support fluent interface patterns', () {
        // Act - Simulate fluent interface
        final model = DefaultModel()
            .copyWith(title: 'Fluent Title')
            .copyWith(isExpanded: true)
            .copyWith(route: '/fluent');

        // Assert
        expect(model.title, equals('Fluent Title'));
        expect(model.isExpanded, isTrue);
        expect(model.route, equals('/fluent'));
      });
    });

    group('Performance and Memory', () {
      test('should create instances efficiently', () {
        // Act
        final startTime = DateTime.now();
        final models = List.generate(
          1000,
          (i) => DefaultModel(title: 'Model $i'),
        );
        final endTime = DateTime.now();

        // Assert
        expect(models.length, equals(1000));
        expect(
          endTime.difference(startTime).inMilliseconds,
          lessThan(100),
        ); // Should be fast
      });

      test('should handle memory efficiently with copyWith', () {
        // Arrange
        final original = DefaultModel(title: 'Original');

        // Act
        final copies = List.generate(
          100,
          (i) => original.copyWith(title: 'Copy $i'),
        );

        // Assert
        expect(copies.length, equals(100));
        expect(copies.every((copy) => copy != original), isTrue);
        expect(
          copies.map((copy) => copy.title).toSet().length,
          equals(100),
        ); // All titles different
      });
    });

    group('Documentation and Examples', () {
      test('should demonstrate typical usage patterns', () {
        // Act - Demonstrate common usage
        final menuItem = DefaultModel(
          title: 'Home',
          subtitle: 'Go to home screen',
          image: 'home_icon.png',
          route: '/home',
          isExpanded: false,
          params: {'section': 'main'},
        );

        final expandedMenuItem = menuItem.copyWith(isExpanded: true);

        // Assert - Verify the patterns work as expected
        expect(menuItem.title, equals('Home'));
        expect(menuItem.isExpanded, isFalse);
        expect(expandedMenuItem.isExpanded, isTrue);
        expect(
          expandedMenuItem.title,
          equals(menuItem.title),
        ); // Other fields preserved
      });

      test('should support navigation patterns', () {
        // Act - Simulate navigation data structure
        final navigationItems = [
          DefaultModel(title: 'Dashboard', route: '/dashboard'),
          DefaultModel(title: 'Profile', route: '/profile'),
          DefaultModel(title: 'Settings', route: '/settings'),
        ];

        final dashboardItem = navigationItems.firstWhere(
          (item) => item.route == '/dashboard',
        );

        // Assert
        expect(dashboardItem.title, equals('Dashboard'));
        expect(dashboardItem.route, equals('/dashboard'));
      });

      test('should demonstrate JSON API integration', () {
        // Act - Simulate API response handling
        final apiResponse = {
          'title': 'API Item',
          'subtitle': 'From API',
          'image': 'api_image.png',
          'route': '/api/item',
          'isExpanded': false,
          'params': {'source': 'api'},
        };

        final model = DefaultModel.fromJson(apiResponse);
        final jsonForApi = model.toJson();

        // Assert
        expect(model.title, equals('API Item'));
        expect(jsonForApi['source'], isNull); // Excluded fields not in JSON
        expect(jsonForApi['params'], equals({'source': 'api'}));
      });
    });
  });
}
