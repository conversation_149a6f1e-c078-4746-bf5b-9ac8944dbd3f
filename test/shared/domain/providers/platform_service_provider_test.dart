import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/i_platform_service.dart';
import 'package:selfeng/shared/domain/platform_service_impl.dart';
import 'package:selfeng/shared/domain/providers/platform_service_provider.dart';
import '../../../helpers/riverpod_test_utils.dart';

void main() {
  group('PlatformServiceProvider Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = RiverpodTestUtils.createTestContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('Provider Definition and Structure', () {
      test('should be defined as a Provider<IPlatformService>', () {
        // Act & Assert
        expect(platformServiceProvider, isA<Provider<IPlatformService>>());
      });

      test('should have correct provider name', () {
        // Act & Assert
        expect(platformServiceProvider.name, isNull); // Default provider name
      });

      test('should have correct provider arguments', () {
        // Act & Assert
        expect(
          platformServiceProvider.argument,
          isNull,
        ); // No arguments expected
      });

      test('should be a synchronous provider', () {
        // Act & Assert
        expect(platformServiceProvider, isA<Provider<IPlatformService>>());
        expect(platformServiceProvider, isNot(isA<FutureProvider>()));
        expect(platformServiceProvider, isNot(isA<StreamProvider>()));
      });
    });

    group('Provider Resolution and Instance Creation', () {
      test('should resolve to IPlatformService instance', () {
        // Act
        final service = container.read(platformServiceProvider);

        // Assert
        expect(service, isA<IPlatformService>());
        expect(service, isNotNull);
      });

      test('should return PlatformServiceImpl instance', () {
        // Act
        final service = container.read(platformServiceProvider);

        // Assert
        expect(service, isA<PlatformServiceImpl>());
        expect(service.runtimeType, PlatformServiceImpl);
      });

      test('should return same instance on each read (Provider caching)', () {
        // Act
        final service1 = container.read(platformServiceProvider);
        final service2 = container.read(platformServiceProvider);
        final service3 = container.read(platformServiceProvider);

        // Assert - Riverpod Provider caches the result, so same instance is returned
        expect(service1, same(service2));
        expect(service2, same(service3));
        expect(service1, same(service3));

        // But all should be PlatformServiceImpl instances
        expect(service1, isA<PlatformServiceImpl>());
        expect(service2, isA<PlatformServiceImpl>());
        expect(service3, isA<PlatformServiceImpl>());
      });

      test('should create valid service instances', () {
        // Act
        final service = container.read(platformServiceProvider);

        // Assert
        expect(service.isAndroid, isA<bool>());
        expect(service.isIOS, isA<bool>());
      });
    });

    group('Service Functionality Integration', () {
      test('should provide working platform detection for Android', () {
        // Act
        final service = container.read(platformServiceProvider);
        final isAndroid = service.isAndroid;

        // Assert
        expect(isAndroid, isA<bool>());
        expect(isAndroid, isNotNull);
      });

      test('should provide working platform detection for iOS', () {
        // Act
        final service = container.read(platformServiceProvider);
        final isIOS = service.isIOS;

        // Assert
        expect(isIOS, isA<bool>());
        expect(isIOS, isNotNull);
      });

      test('should maintain platform detection consistency', () {
        // Act
        final service = container.read(platformServiceProvider);
        final androidResult1 = service.isAndroid;
        final iosResult1 = service.isIOS;
        final androidResult2 = service.isAndroid;
        final iosResult2 = service.isIOS;

        // Assert - Results should be consistent for the same instance
        expect(androidResult1, equals(androidResult2));
        expect(iosResult1, equals(iosResult2));
      });

      test('should handle platform detection without exceptions', () {
        // Act & Assert
        final service = container.read(platformServiceProvider);

        expect(() => service.isAndroid, returnsNormally);
        expect(() => service.isIOS, returnsNormally);

        final androidResult = service.isAndroid;
        final iosResult = service.isIOS;

        expect(androidResult, isA<bool>());
        expect(iosResult, isA<bool>());
      });
    });

    group('Provider Behavior and Lifecycle', () {
      test('should work correctly with ProviderScope', () {
        // Act
        final service = container.read(platformServiceProvider);

        // Assert
        expect(service, isA<IPlatformService>());
        expect(service.isAndroid, isA<bool>());
        expect(service.isIOS, isA<bool>());
      });

      test('should handle multiple container instances', () {
        // Act
        final container1 = RiverpodTestUtils.createTestContainer();
        final container2 = RiverpodTestUtils.createTestContainer();

        final service1 = container1.read(platformServiceProvider);
        final service2 = container2.read(platformServiceProvider);

        // Assert
        expect(service1, isA<IPlatformService>());
        expect(service2, isA<IPlatformService>());
        expect(
          service1,
          isNot(same(service2)),
        ); // Different containers, different instances

        // Cleanup
        container1.dispose();
        container2.dispose();
      });

      test('should work with provider overrides', () {
        // Arrange
        final mockService = _MockPlatformService();
        final override = platformServiceProvider.overrideWithValue(mockService);
        final testContainer = RiverpodTestUtils.createTestContainer(
          additionalOverrides: [override],
        );

        // Act
        final service = testContainer.read(platformServiceProvider);

        // Assert
        expect(service, same(mockService));
        expect(service.isAndroid, isTrue); // Mock returns true
        expect(service.isIOS, isFalse); // Mock returns false

        // Cleanup
        testContainer.dispose();
      });

      test('should handle provider disposal gracefully', () {
        // Act
        final service = container.read(platformServiceProvider);

        // Assert - Service should still work after container operations
        expect(service.isAndroid, isA<bool>());
        expect(service.isIOS, isA<bool>());
      });
    });

    group('Provider Dependencies and Integration', () {
      test('should not depend on other providers', () {
        // Act
        final service = container.read(platformServiceProvider);

        // Assert - Should work without any dependencies
        expect(service, isA<IPlatformService>());
        expect(service.isAndroid, isA<bool>());
        expect(service.isIOS, isA<bool>());
      });

      test('should work in isolation from other providers', () {
        // Act - Read the provider multiple times
        final services = List.generate(
          5,
          (_) => container.read(platformServiceProvider),
        );

        // Assert - All services should be valid and independent
        for (final service in services) {
          expect(service, isA<IPlatformService>());
          expect(service.isAndroid, isA<bool>());
          expect(service.isIOS, isA<bool>());
        }

        // Verify they are the same instance (due to Provider caching)
        for (int i = 0; i < services.length - 1; i++) {
          for (int j = i + 1; j < services.length; j++) {
            expect(services[i], same(services[j]));
          }
        }
      });

      test('should maintain service interface contract', () {
        // Act
        final service = container.read(platformServiceProvider);

        // Assert - Should work seamlessly with interface
        expect(service.isAndroid, isA<bool>());
        expect(service.isIOS, isA<bool>());

        // Verify interface compliance
        expect(service, isA<IPlatformService>());
      });
    });

    group('Error Handling and Edge Cases', () {
      test('should handle rapid consecutive reads', () {
        // Act - Rapid reads to test for any performance or stability issues
        final services = <IPlatformService>[];
        for (int i = 0; i < 100; i++) {
          services.add(container.read(platformServiceProvider));
        }

        // Assert - All services should be valid
        for (final service in services) {
          expect(service, isA<IPlatformService>());
          expect(service.isAndroid, isA<bool>());
          expect(service.isIOS, isA<bool>());
        }

        // Verify all are the same instance (due to Provider caching)
        final uniqueServices = services.toSet();
        expect(uniqueServices.length, equals(1));
        expect(services.every((service) => service == services.first), isTrue);
      });

      test('should work correctly in async context', () async {
        // Act - Test in async context
        IPlatformService? asyncService;

        await Future.microtask(() {
          asyncService = container.read(platformServiceProvider);
        });

        // Assert
        expect(asyncService, isNotNull);
        expect(asyncService, isA<IPlatformService>());
        expect(asyncService!.isAndroid, isA<bool>());
        expect(asyncService!.isIOS, isA<bool>());
      });

      test('should handle container recreation', () {
        // Act - Dispose and recreate container
        container.dispose();
        final newContainer = RiverpodTestUtils.createTestContainer();

        final service = newContainer.read(platformServiceProvider);

        // Assert
        expect(service, isA<IPlatformService>());
        expect(service.isAndroid, isA<bool>());
        expect(service.isIOS, isA<bool>());

        // Cleanup
        newContainer.dispose();
      });
    });

    group('Provider Documentation and Examples', () {
      test('should demonstrate typical provider usage pattern', () {
        // Act - Demonstrate common usage
        final platformService = container.read(platformServiceProvider);
        final isMobile = platformService.isAndroid || platformService.isIOS;
        final isAndroid = platformService.isAndroid;
        final isIOS = platformService.isIOS;

        // Assert - Verify the logic works as expected
        expect(isMobile, isA<bool>());
        expect(isAndroid, isA<bool>());
        expect(isIOS, isA<bool>());

        // Cross-verification
        expect(
          isAndroid && isIOS,
          isFalse,
          reason: 'Cannot be both Android and iOS simultaneously',
        );
      });

      test('should provide clear platform detection capabilities', () {
        // Act
        final service = container.read(platformServiceProvider);

        // Assert & Document - Provider should enable clear platform detection
        expect(
          service.isAndroid,
          isA<bool>(),
          reason: 'Provider should enable Android platform detection',
        );
        expect(
          service.isIOS,
          isA<bool>(),
          reason: 'Provider should enable iOS platform detection',
        );
      });

      test('should support dependency injection patterns', () {
        // Act - Simulate dependency injection usage
        final injectedService = container.read(platformServiceProvider);

        // Assert - Should work correctly when injected
        expect(injectedService, isA<IPlatformService>());
        expect(injectedService.isAndroid, isA<bool>());
        expect(injectedService.isIOS, isA<bool>());
      });
    });

    group('Provider Testing Best Practices', () {
      test('should support unit testing with mocks', () {
        // Arrange
        final mockService = _MockPlatformService();
        final override = platformServiceProvider.overrideWithValue(mockService);
        final testContainer = RiverpodTestUtils.createTestContainer(
          additionalOverrides: [override],
        );

        // Act
        final service = testContainer.read(platformServiceProvider);

        // Assert
        expect(service, same(mockService));
        expect(service.isAndroid, isTrue); // Mock behavior
        expect(service.isIOS, isFalse); // Mock behavior

        // Cleanup
        testContainer.dispose();
      });

      test('should work with Riverpod testing utilities', () {
        // Act & Assert - Use the testing utilities
        RiverpodTestUtils.testProviderInitialState(
          platformServiceProvider,
          isA<IPlatformService>(),
        );
      });

      test('should support provider disposal testing', () {
        // Act & Assert
        RiverpodTestUtils.testProviderDisposal(platformServiceProvider);
      });
    });
  });
}

/// Mock implementation for testing
class _MockPlatformService implements IPlatformService {
  @override
  bool get isAndroid => true;

  @override
  bool get isIOS => false;
}
