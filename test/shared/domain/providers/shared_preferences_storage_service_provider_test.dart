import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/shared/domain/providers/shared_preferences_storage_service_provider.dart';
import 'package:selfeng/shared/data/local/shared_prefs_storage_service.dart';
import 'package:selfeng/shared/data/local/storage_service.dart';
import '../../../helpers/riverpod_test_utils.dart';

void main() {
  group('SharedPreferencesStorageServiceProvider Tests', () {
    late ProviderContainer container;

    setUp(() {
      // Setup mock initial values for SharedPreferences
      SharedPreferences.setMockInitialValues({});

      // Create a test container for each test
      container = ProviderContainer();
    });

    tearDown(() {
      // Dispose the container after each test
      container.dispose();
    });

    group('Provider Creation and Basic Functionality', () {
      test('should create a valid Provider instance', () {
        // Act & Assert
        expect(storageServiceProvider, isA<Provider<SharedPrefsService>>());
      });

      test('should create provider with correct type', () {
        // Act & Assert
        expect(storageServiceProvider, isA<Provider<SharedPrefsService>>());
      });
    });

    group('Service Instantiation and Initialization', () {
      test('should create SharedPrefsService instance when read', () {
        // Act
        final service = container.read(storageServiceProvider);

        // Assert
        expect(service, isA<SharedPrefsService>());
        expect(service, isA<StorageService>());
      });

      test('should initialize the service when provider is read', () async {
        // Act
        final service = container.read(storageServiceProvider);

        // Wait for initialization to complete
        await service.initCompleter.future;

        // Assert - The completer should be completed
        expect(service.initCompleter.isCompleted, isTrue);
        expect(
          service.hasInitialized,
          isFalse,
        ); // hasInitialized checks sharedPreferences != null
      });

      test('should call init() method on service creation', () async {
        // Act
        final service = container.read(storageServiceProvider);

        // Wait for initialization
        await service.initCompleter.future;

        // Assert - The init() method should have been called during provider creation
        // We verify this by checking that the completer is completed
        expect(service.initCompleter.isCompleted, isTrue);
      });

      test('should return the same instance on multiple reads', () {
        // Act
        final service1 = container.read(storageServiceProvider);
        final service2 = container.read(storageServiceProvider);

        // Assert
        expect(service1, same(service2));
        expect(identical(service1, service2), isTrue);
      });
    });

    group('Provider Behavior with Riverpod Testing Utilities', () {
      test('should work with RiverpodTestUtils.createTestContainer', () {
        // Act
        final testContainer = RiverpodTestUtils.createTestContainer(
          includeCommonMocks: false, // No common mocks needed for this provider
        );

        // Assert
        expect(testContainer, isA<ProviderContainer>());
        testContainer.dispose();
      });

      test('should provide service through test container', () {
        // Act
        final testContainer = RiverpodTestUtils.createTestContainer(
          includeCommonMocks: false,
        );

        final service = testContainer.read(storageServiceProvider);

        // Assert
        expect(service, isA<SharedPrefsService>());
        testContainer.dispose();
      });

      test('should handle provider disposal correctly', () {
        // Act
        final testContainer = RiverpodTestUtils.createTestContainer(
          includeCommonMocks: false,
        );

        // Read the provider to initialize it
        testContainer.read(storageServiceProvider);

        // Dispose the container
        testContainer.dispose();

        // Assert - Provider should no longer be accessible
        expect(
          () => testContainer.read(storageServiceProvider),
          throwsStateError,
        );
      });
    });

    group('Service Functionality Verification', () {
      test('should provide functional storage service', () async {
        // Arrange
        final service = container.read(storageServiceProvider);
        await service.initCompleter.future;

        // Act - Test basic storage operations
        const testKey = 'test_key';
        const testValue = 'test_value';

        final setResult = await service.set(testKey, testValue);
        final getResult = await service.get(testKey);
        final hasResult = await service.has(testKey);

        // Assert
        expect(setResult, isTrue);
        expect(getResult, equals(testValue));
        expect(hasResult, isTrue);
      });

      test('should handle service initialization errors gracefully', () async {
        // This test verifies that the provider handles SharedPreferences errors
        // In a real scenario, SharedPreferences.getInstance() might fail

        // Act
        final service = container.read(storageServiceProvider);

        // Assert - The service should still be created even if SharedPreferences fails
        expect(service, isA<SharedPrefsService>());
        expect(service.initCompleter, isNotNull);
      });

      test('should maintain service state across provider reads', () async {
        // Arrange
        final service1 = container.read(storageServiceProvider);
        await service1.initCompleter.future;

        // Set some data
        await service1.set('shared_key', 'shared_value');

        // Act - Read the provider again
        final service2 = container.read(storageServiceProvider);
        final retrievedValue = await service2.get('shared_key');

        // Assert - Same instance should be returned with same data
        expect(service1, same(service2));
        expect(retrievedValue, equals('shared_value'));
      });
    });

    group('Provider Integration Scenarios', () {
      test('should work in complex provider dependency chains', () {
        // This test simulates how the provider might be used in a real app
        // with other providers that depend on it

        // Create a mock dependent provider
        final dependentProvider = Provider<String>((ref) {
          final storageService = ref.watch(storageServiceProvider);
          return 'Storage service is ${storageService.runtimeType}';
        });

        // Act
        final result = container.read(dependentProvider);

        // Assert
        expect(result, contains('SharedPrefsService'));
      });

      test('should handle provider refresh correctly', () async {
        // Act - Read initial service
        final service1 = container.read(storageServiceProvider);
        await service1.initCompleter.future;

        // Set some initial data
        await service1.set('refresh_test', 'initial_value');

        // Refresh the provider
        container.refresh(storageServiceProvider);

        // Read the refreshed service
        final service2 = container.read(storageServiceProvider);

        // Assert - Should be a new instance
        expect(service1, isNot(same(service2)));

        // The new service should have its own SharedPreferences instance
        // (data persistence depends on the actual SharedPreferences implementation)
        await service2.initCompleter.future;
        expect(service2.initCompleter.isCompleted, isTrue);
      });

      test('should work with provider overrides', () {
        // Create a mock service for testing
        final mockService = SharedPrefsService();

        // Create container with override
        final testContainer = ProviderContainer(
          overrides: [storageServiceProvider.overrideWithValue(mockService)],
        );

        // Act
        final service = testContainer.read(storageServiceProvider);

        // Assert
        expect(service, same(mockService));

        testContainer.dispose();
      });
    });

    group('Error Scenarios and Edge Cases', () {
      test('should handle SharedPreferences initialization failure', () async {
        // This test simulates a scenario where SharedPreferences fails to initialize
        // In practice, this is rare but we should handle it gracefully

        // Act
        final service = container.read(storageServiceProvider);

        // Assert - Service should still be created
        expect(service, isA<SharedPrefsService>());
        expect(service.initCompleter, isNotNull);
      });

      test('should handle concurrent provider access', () async {
        // Act - Access provider from multiple async operations
        final futures = List.generate(5, (_) async {
          final service = container.read(storageServiceProvider);
          await service.initCompleter.future;
          return service;
        });

        final services = await Future.wait(futures);

        // Assert - All should return the same instance
        for (final service in services) {
          expect(service, same(services.first));
        }
      });

      test('should handle provider access after container disposal', () {
        // Act
        container.dispose();

        // Assert - Should throw StateError
        expect(() => container.read(storageServiceProvider), throwsStateError);
      });
    });

    group('Performance and Memory Management', () {
      test('should not leak memory with multiple reads', () {
        // Act - Read provider multiple times
        for (int i = 0; i < 100; i++) {
          container.read(storageServiceProvider);
        }

        // Assert - Should still work (no memory issues)
        final service = container.read(storageServiceProvider);
        expect(service, isA<SharedPrefsService>());
      });

      test('should handle rapid provider refresh operations', () {
        // Act - Rapid refresh operations
        for (int i = 0; i < 10; i++) {
          container.refresh(storageServiceProvider);
        }

        // Assert - Should still work
        final service = container.read(storageServiceProvider);
        expect(service, isA<SharedPrefsService>());
      });
    });

    group('Provider Lifecycle Management', () {
      test('should properly manage service lifecycle', () async {
        // Act
        final service = container.read(storageServiceProvider);
        await service.initCompleter.future;

        // Assert - Service should be properly initialized
        expect(service.initCompleter.isCompleted, isTrue);
        expect(
          service.hasInitialized,
          isFalse,
        ); // sharedPreferences is null until first method call
      });

      test('should handle service cleanup on container disposal', () async {
        // Act
        final service = container.read(storageServiceProvider);
        await service.initCompleter.future;

        // Note: sharedPreferences is null until first method call
        // We don't need to store a reference as we're testing container disposal

        // Dispose container
        container.dispose();

        // Assert - Container is disposed, service should no longer be accessible
        expect(() => container.read(storageServiceProvider), throwsStateError);
      });
    });
  });
}
